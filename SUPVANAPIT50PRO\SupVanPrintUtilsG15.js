import bleManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import baseSupPrint from"\u002E\u002F\u0042\u0061\u0073\u0065\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u002E\u006A\u0073";import ptFlag from"\u002E\u002F\u0050\u0052\u0049\u004E\u0054\u0045\u0052\u005F\u0046\u004C\u0041\u0047\u002E\u006A\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u006A\u0073";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{'\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C':0x10,'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':0x11,"CMD_CHECK_DEVICE":0x12,'\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054':0x13,"CMD_NEXTFRM_FIRMWARE_BULK":0xc6,'\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054':0x14,"CMD_NEXT_ZIPPEDBULK":0x5c,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0046\u0049\u0052\u004D\u0057\u0041\u0052\u0045\u005F\u0052\u0045\u0056':0xc5,'\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052':0xf0,"CMD_SET_RFID_DATA":0x5d,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0052\u0045\u0056':0x17,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0044\u0050\u0049':0x22,"ERROR_CMD_INQUIRY_STA":123,'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041\u005F\u0044\u0050\u0049':456,"dataLength":500,"cnt":0,"processSteps":'initQueryStatus',"frameNumber":0,'\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064':0,'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074':[],"isStop":false,'\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061':[],"deviceSn":'','\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065':1,"gap":3,"speed":30,"noInstallType":false,'\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D':0,'\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D':0,"prtStarNum":0,'\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D':0,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'','\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,'\u0064\u0069\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0072\u0065\u0061\u0064\u0044\u0070\u0069\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065':{'\u006D\u0073\u0067\u0054\u0079\u0070\u0065':"\u0030",'\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D':0},notifyData(notifyMessage){console['\u006C\u006F\u0067']("\u636E\u6570\u5230\u6536\u63A5".split("").reverse().join(""),JSON['\u0073\u0074\u0072\u0069\u006E\u0067\u0069\u0066\u0079'](notifyMessage));this['\u0068\u0061\u006E\u0064\u006C\u0065\u004E\u006F\u0074\u0069\u0066\u0079'](notifyMessage);},doSupVanPrint(supvanImageData,nObjectData){var _0x47cbe;const that=this;_0x47cbe=959934^959933;console['\u006C\u006F\u0067']("\u0073\u0075\u0070\u0076\u0061\u006E\u0070\u0072\u0069\u006E\u0074\u0075\u0074\u0069\u006C\u0073\u0067\u0031\u0035");let objectData=nObjectData;that['\u0069\u0073\u0053\u0074\u006F\u0070']=false;that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=402555^402555;ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074']=299983^299983;that['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']=600372^600372;that['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=304873^304833;that['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=343737^343697;that['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=523629^523589;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=supvanImageData;console['\u006C\u006F\u0067'](" :\u6A21\u5B57\u5370\u6253".split("").reverse().join(""),supvanImageData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();that['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="sutatSyreuQtini".split("").reverse().join("");that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=objectData['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];that['\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']=objectData['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065'];that['\u0067\u0061\u0070']=objectData['\u0047\u0061\u0070'];that['\u0073\u0070\u0065\u0065\u0064']=objectData['\u0053\u0070\u0065\u0065\u0064'];that['\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074']();},startPrint(){this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],900543^900543);},queryDPI(callBack){this['\u0064\u0069\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=callBack;this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041\u005F\u0044\u0050\u0049'];console['\u006C\u006F\u0067']("\u6001\u72B6\u8BE2\u67E5".split("").reverse().join(""));baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],319961^319961);},handleNotify(notifyMessage){switch(this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']){case this['\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0044\u0050\u0049']:var _0x8f37ac=(927180^927176)+(938854^938853);let dpiValue=(notifyMessage[712972^712986]&(780483^780348))+((notifyMessage[484420^484435]&(206288^206127))<<(568034^568042));_0x8f37ac='\u0065\u006B\u0062\u0064\u006F\u006A';console['\u006C\u006F\u0067']("eulaVipd".split("").reverse().join(""),dpiValue);this['\u0072\u0065\u0061\u0064\u0044\u0070\u0069\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](dpiValue);break;case this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041\u005F\u0044\u0050\u0049']:if(baseSupPrint['\u0068\u0061\u006E\u0064\u006C\u0065\u0049\u006E\u0071\u0075\u0069\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073'](notifyMessage)){console['\u006C\u006F\u0067']("\u8FD4\u56DE\u7684\u0064\u0070\u0069",ptFlag['\u0063\u0068\u0061\u006E\u0067\u0065\u0044\u0070\u0069']);this['\u0064\u0069\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](ptFlag['\u0063\u0068\u0061\u006E\u0067\u0065\u0044\u0070\u0069']);}break;case this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045']:this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();break;case this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041']:if(baseSupPrint['\u0068\u0061\u006E\u0064\u006C\u0065\u0049\u006E\u0071\u0075\u0069\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073'](notifyMessage)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();break;case this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);break;case this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0054\u0068\u0069\u0072\u0064']();break;case this['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C']:this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=619172^619148;this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=878667^878691;this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=830197^830173;console['\u006C\u006F\u0067']("\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u63A5\u6536\u5230\u5FD7\u6EE1\u5B8C\u6210\u547D\u4EE4\u56DE\u590D\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A");if(this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']>(303535^303535)){console['\u006C\u006F\u0067']("\u533A\u51B2\u7F13\u4E2A\u4E00\u4E0B\u53D1\u4E0B".split("").reverse().join(""));this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},391843^391787);}else{this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']=this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']+(803101^803100);if(this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']>=bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){console['\u006C\u006F\u0067']("\u6253\u5370\u5B8C\u6210");this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0049\u006D\u0061\u0067\u0065']();}else if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004D\u0061\u0074\u0072\u0069\u0078']();}}}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=467563^467563;if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();break;}},handleStep(){switch(this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']){case"\u0069\u006E\u0069\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(111970^111971)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0063\u0068\u0065\u0063\u006B\u0044\u0065\u0076\u0069\u0063\u0065";this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'],444881^444881);break;case"tnirPtrats".split("").reverse().join(""):if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u7EC8\u6B62\u6253\u5370\u6210\u529F"));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}else{setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},502625^502697);}break;case"\u0073\u0065\u006E\u0064\u004D\u0061\u0074\u0072\u0069\u0078\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u0062\u0075\u0066\u0053\u0074\u0061']==(890517^890517)){if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(490751^490750)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']<=(396507^396507)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},235919^235965);}}else{this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']--;if(this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']<=(808319^808319)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},731358^731372);}break;case"sutatSyreuQtnirPpots".split("").reverse().join(""):this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'],920920^920920);break;case"eciveDkcehc".split("").reverse().join(""):this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']<=(447014^447014)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u006D\u0045\u0078\u0065\u0053\u0074\u0061']==(440002^440002)){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="tnirPtrats".split("").reverse().join("");this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'],970724^970724);}else{setTimeout(()=>{this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},439689^439739);}break;case"hsiniFtnirp".split("").reverse().join(""):console['\u006C\u006F\u0067']("noitatSgnitnirp".split("").reverse().join(""),ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']);this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']<=(207787^207787)){return;}if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(383581^383580)){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074'];if(this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']==(292397^292397)){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"num":this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']}));}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']));}}break;}},handleTransferNext(){if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u529F\u6210\u5370\u6253\u6B62\u7EC8".split("").reverse().join("")));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0065\u006E\u0064\u004D\u0061\u0074\u0072\u0069\u0078\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},handleTransferStep(btData){if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](!![],!![])){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0063\u006E\u0074']=parseInt((btData['\u006C\u0065\u006E\u0067\u0074\u0068']+this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']-(193018^193019))/this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050\u0053\u0074\u0061\u0072\u0074\u0054\u0072\u0061\u006E\u0073'](this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'],708538^708026,this['\u0063\u006E\u0074']);},handleTransferStepThird(){var _0x298c=(610685^610685)+(154095^154087);const that=this;_0x298c='\u0070\u0061\u0064\u0068\u0062\u006A';that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']++;if(that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']<that['\u0063\u006E\u0074']){that['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{setTimeout(()=>{that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']=550445^550445;that['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'],198066^198066);},786819^786825);}},handleTransferStepSecond(btData){var _0x2b23g=(387428^387428)+(566012^566015);let btWrite=new Uint8Array(this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']+(367269^367267));_0x2b23g=(696885^696893)+(378017^378024);btWrite[661989^661989]=393386^393216;btWrite[283391^283390]=745218^745401;btWrite[975890^975888]=190958^190958;btWrite[976474^976473]=922741^922741;btWrite[834783^834779]=this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072'];btWrite[238192^238197]=this['\u0063\u006E\u0074'];if((this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+(681019^681018))*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']<btData['\u006C\u0065\u006E\u0067\u0074\u0068']){for(var k=231272^231272;k<this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];k++){btWrite[(134029^134027)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}else{var _0x_0x537=(399187^399195)+(856554^856553);let nc=btData['\u006C\u0065\u006E\u0067\u0074\u0068']-this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];_0x_0x537="nhagjl".split("").reverse().join("");for(var k=778974^778974;k<nc;k++){btWrite[(988037^988035)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}let chcksum=504756^504756;for(var i=324768^324772;i<btWrite['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){chcksum+=btWrite[i]&(447129^447078);}btWrite[905244^905246]=chcksum;btWrite[400165^400166]=chcksum>>(281330^281338);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052'];this['\u0074\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0044\u0061\u0074\u0061\u004C\u0050'](btWrite);},transferDataLP(btdata){let message=new Uint8Array(930945^931457);message[556550^556550]=189206^189288;message[860789^860788]=190840^190754;message[258936^258938]=635141^635385;message[725364^725367]=402354^402355;message[136722^136726]=600652^600668;message[681310^681307]=329574^329572;for(var i=516833^516839;i<btdata['\u006C\u0065\u006E\u0067\u0074\u0068']+(106874^106876);i++){message[i]=btdata[i-(966822^966816)];}this['\u0073\u0065\u006E\u0064\u0044\u0061\u0074\u0061\u0047\u0031\u0035'](message);},sendDataG15(message){var _0x62d=(842914^842916)+(124923^124915);let byteArrayList=[];_0x62d=(860279^860276)+(202553^202544);var _0xbae1bd=(341318^341316)+(251994^251987);let i=894394^894394;_0xbae1bd=(857507^857510)+(745990^745989);var _0xf33ae=(655014^655014)+(108291^108289);let count=974821^974821;_0xf33ae=(129927^129926)+(951892^951892);while(i<(484258^484262)){let listData=message['\u0073\u0075\u0062\u0061\u0072\u0072\u0061\u0079'](i*(153469^153597),(181013^181141)+i*(618520^618648));byteArrayList['\u0070\u0075\u0073\u0068'](listData);i++;}var _0x6c696f;var timer=setInterval(()=>{if(count<(943774^943770)){bleManage['\u006F\u006E\u0057\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065'](byteArrayList[count]);count++;}else{clearInterval(timer);}},393960^393940);_0x6c696f=903405^903405;},stopPrint(){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";console['\u006C\u006F\u0067']("\u67E5\u8BE2\u72B6\u6001");this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();return false;},devCheckErrMsg(isBusy,isPrintFinish){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=false;if(ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']!=(649477^649477)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0036'];console['\u006C\u006F\u0067']("\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E",ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']!=(242512^242512)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0037'];console['\u006C\u006F\u0067']("dellatsnIton".split("").reverse().join(""),ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']!=(577236^577236)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0039'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072",ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']!=(780311^780311)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0030'];console['\u006C\u006F\u0067']("rorrEedoMlebal".split("").reverse().join(""),ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']!=(212868^212868)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0031'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065\u72B6\u0031\u6001\u503C",ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']!=(307026^307026)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0033'];console['\u006C\u006F\u0067']("\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072\u72B6\u0031\u6001\u503C",ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}if(isBusy){if(ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']==(465063^465063)&&ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(976439^976439)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0032'];console['\u006C\u006F\u0067']("ysuBecived".split("").reverse().join(""),ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}}return false;},doPrintNextPage(nImageDataListNext){var _0x700b=(816671^816663)+(250851^250855);let imageDataListNext=nImageDataListNext;_0x700b=660018^660021;this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=imageDataListNext;console['\u006C\u006F\u0067']("\u7B2C\u4E8C\u4EFD\u6253\u5370\u5B57\u6A21\u957F\u5EA6\u003A\u0020",this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']);this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;},printFinish(){setTimeout(()=>{this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068";this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},224051^224039);},stopPrintSupvan(){this['\u0069\u0073\u0053\u0074\u006F\u0070']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];},queryStatus(){this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],756912^756912);},stopPrintCallback(stopCallBack){this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=stopCallBack;}};