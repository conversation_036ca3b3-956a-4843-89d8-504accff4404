import supPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import canvasDataRGBAUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0061\u0074\u0061\u0052\u0047\u0042\u0041\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import barCodeUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageEncodeUtilsT50Pro from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0054\u0035\u0030\u0050\u0072\u006F\u002E\u006A\u0073";import imageEncodeUtilsMp50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import drawQrcode from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0077\u0065\u0061\u0070\u0070\u002E\u0071\u0072\u0063\u006F\u0064\u0065\u002E\u0065\u0073\u006D\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import bleTool from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import SupVanPrintUtilsMP50 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import SupVanPrintUtilsG15 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import imageEncodeUtilsG15 from"\u002E\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import SupVanPrintUtilsG21 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import imageEncodeUtilsG21 from"\u002E\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";export default{'\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E':'','\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061':{"width":'','\u0068\u0065\u0069\u0067\u0068\u0074':'','\u0070\u0061\u0074\u0068':''},'\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068':'','\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065':{"width":'',"height":'',"barcodeWidth":'','\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074':'',"qrcodeWidth":'','\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074':''},'\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074':{},'\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],"myCanvasRGBA":null,"canvasBarCode":null,'\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D':0,'\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068':400,'\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074':302,'\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C':null,"connectCallBack":null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058'],'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':8,async doPrintMatrix(myCanvasRGBA,nObjectData,canvasBarCode,callback){try{var _0xda1adf=(842969^842971)+(536778^536781);const that=this;_0xda1adf="pbmjgp".split("").reverse().join("");var _0xf5g51c=(115855^115846)+(111548^111541);let objectData=nObjectData;_0xf5g51c=(145769^145773)+(256371^256370);if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u7A7A\u4E3A\u80FD\u4E0DsavnaC\u672C\u6587".split("").reverse().join(""));}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](objectData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](330475^330467);if(bleTool['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectData[825906^825906]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0071\u0075\u0065\u0072\u0079\u0047\u0031\u0035\u0044\u0070\u0069']());}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectData[390617^390617]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u0047\u0032\u0031\u0044\u0070\u0069'](objectData[967274^967274]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}else if(bleTool['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectData[706534^706534]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u004D\u0050\u0035\u0030\u0044\u0070\u0069'](objectData[940261^940261]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("\u0064\u0070\u0069\u0076\u0061\u006C\u0075\u0065",that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);that['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback,constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058']);that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,objectData,canvasBarCode);var _0x1e_0x397=(484859^484858)+(516619^516616);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();_0x1e_0x397=(433340^433337)+(506215^506214);callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async queryG15Dpi(){return new Promise(resolve=>{SupVanPrintUtilsG15['\u0071\u0075\u0065\u0072\u0079\u0044\u0050\u0049'](result=>{var _0xc2dc=(192524^192523)+(499023^499016);let dpi=result;_0xc2dc=583115^583106;let dpiValue=dpi/(352680^352716);if(dpi<(129048^129700)||dpi>(202915^203651)){dpiValue=567030^567038;}resolve(Math['\u0063\u0065\u0069\u006C'](dpiValue));});});},async readG21Dpi(deviceSn){return new Promise(resolve=>{SupVanPrintUtilsG21['\u0072\u0065\u0061\u0064\u0044\u0050\u0049'](result=>{console['\u006C\u006F\u0067']("\u0072\u0065\u0073\u0075\u006C\u0074",result);var _0xef55ga=(945116^945117)+(358815^358809);let dpi=result;_0xef55ga=(333760^333764)+(526885^526883);var _0x2a_0x8cb=(676356^676365)+(108704^108705);let dpiValue=dpi/(531302^531202);_0x2a_0x8cb=(395066^395069)+(517327^517320);if(dpi<(891173^891801)||dpi>(529767^529991)){dpiValue=699418^699410;}resolve(Math['\u0063\u0065\u0069\u006C'](dpiValue));});});},async readMP50Dpi(deviceSn){return new Promise(resolve=>{SupVanPrintUtilsMP50['\u0072\u0065\u0061\u0064\u0044\u0050\u0049'](result=>{console['\u006C\u006F\u0067']("tluser".split("").reverse().join(""),result);let dpi=result;var _0x3e_0xb70=(279278^279273)+(889740^889741);let dpiValue=dpi/(280028^279992);_0x3e_0xb70=413166^413163;let MP50Device=bleTool['\u006D\u0070\u0035\u0030\u004F\u006E\u006C\u0079\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](deviceSn);if(MP50Device){if(dpi<(594247^594939)||dpi>(319088^318964)){dpiValue=824913^824921;}else{dpiValue=Math['\u0063\u0065\u0069\u006C'](dpi/(263598^263626));}}else{if(dpi<(283749^282665)||dpi>(683047^682291)){dpiValue=702027^702023;}else{dpiValue=Math['\u0063\u0065\u0069\u006C'](dpi/(754896^754868));}}resolve(Math['\u0063\u0065\u0069\u006C'](dpiValue));});});},async printNextMatrix(){try{const that=this;const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async matrixCanvas(objectList){try{var _0xg2d75c;const that=this;_0xg2d75c=231800^231800;var _0xd_0xdfc;const objectData=await that['\u0071\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](objectList);_0xd_0xdfc="fpiplo".split("").reverse().join("");await canvasDataRGBAUtils['\u006D\u0061\u0074\u0072\u0069\u0078\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},matrixCopies(myCanvasRGBA,objectData,canvasBarCode){var _0xa2e=(626706^626706)+(702947^702946);const that=this;_0xa2e=109407^109406;that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']=canvasBarCode;that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];let objectListAllFirst=objectData['\u0073\u006C\u0069\u0063\u0065']();for(let object of objectListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(484716^484716)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=689401^689400;}for(let i=158845^158845;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](objectData);},async doPrintImage(myCanvasRGBA,pageImageListData,callback){try{const that=this;if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u6587\u672C\u0043\u0061\u006E\u0076\u0061\u0073\u4E0D\u80FD\u4E3A\u7A7A");}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](pageImageListData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](970710^970718);if(bleTool['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](pageImageListData[906294^906294]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0071\u0075\u0065\u0072\u0079\u0047\u0031\u0035\u0044\u0070\u0069']());}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](pageImageListData[845474^845474]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u0047\u0032\u0031\u0044\u0070\u0069'](pageImageListData[508330^508330]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}else if(bleTool['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](pageImageListData[447938^447938]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](await that['\u0072\u0065\u0061\u0064\u004D\u0050\u0035\u0030\u0044\u0070\u0069'](pageImageListData[131556^131556]['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']));}that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("eulavipd".split("").reverse().join(""),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);that['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback,constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045']);that['\u0069\u006D\u0061\u0067\u0065\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,pageImageListData);const objectData=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await canvasDataRGBAUtils['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},async printNextImage(){try{var _0xb7948a;const that=this;_0xb7948a=555841^555844;const objectData=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();await canvasDataRGBAUtils['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},imageCopies(myCanvasRGBA,pageImageListData){var _0xf446e=(789403^789400)+(709449^709452);const that=this;_0xf446e="bhbaco".split("").reverse().join("");that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];var _0x28d=(166992^166999)+(449393^449397);let imageListAllFirst=pageImageListData['\u0073\u006C\u0069\u0063\u0065']();_0x28d=962572^962572;for(let object of imageListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(967598^967598)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=591413^591412;}for(let i=201014^201014;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](pageImageListData);},async doDrawPreview(myCanvasRGBA,nObjectData,canvasBarCode,callback){try{const that=this;let objectData=nObjectData;bleTool['\u0073\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065'](678350^678342);that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();if(!myCanvasRGBA){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0038'],"\u7A7A\u4E3A\u80FD\u4E0DsavnaC\u672C\u6587".split("").reverse().join(""));}if(constants['\u0069\u0073\u0041\u0072\u0072\u0061\u0079\u0045\u006D\u0070\u0074\u0079'](objectData)){return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0036'],"\u6A21\u677F\u5BF9\u8C61\u4E0D\u80FD\u4E3A\u7A7A");}canvasDataRGBAUtils['\u0064\u0072\u0061\u0077\u0050\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u006F\u0070\u0069\u0065\u0073'](myCanvasRGBA,objectData,canvasBarCode);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();callback(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065'],constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0030']));await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async drawNextPreview(){try{var _0xd776d;const that=this;_0xd776d=(631706^631711)+(702190^702190);const objectList=await that['\u0061\u006E\u0061\u006C\u0079\u0073\u0069\u0073\u0054\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0044\u0061\u0074\u0061']();await that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073'](objectList);}catch(error){throw error;}},async previewCanvas(objectList){try{var _0x72fe6f=(831193^831197)+(200518^200515);const that=this;_0x72fe6f=205485^205487;const objectData=await that['\u0071\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](objectList);canvasDataRGBAUtils['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0044\u0061\u0074\u0061\u0043\u0061\u006E\u0076\u0061\u0073'](objectData);}catch(error){throw error;}},async previewCopies(myCanvasRGBA,objectData,canvasBarCode){var _0x9b58dc=(818495^818487)+(707321^707322);const that=this;_0x9b58dc=(315984^315990)+(501900^501898);that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0050\u0052\u0045\u0056\u0049\u0045\u0057'];that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061']();that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=myCanvasRGBA;that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']=canvasBarCode;that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];var _0xe0e5a;let objectListAllFirst=objectData['\u0073\u006C\u0069\u0063\u0065']();_0xe0e5a='\u0067\u006A\u0064\u0069\u0069\u0067';for(let object of objectListAllFirst){if(!object['\u0043\u006F\u0070\u0069\u0065\u0073']||object['\u0043\u006F\u0070\u0069\u0065\u0073']==(439168^439168)){object['\u0043\u006F\u0070\u0069\u0065\u0073']=608051^608050;}for(let i=150421^150421;i<object['\u0043\u006F\u0070\u0069\u0065\u0073'];i++){that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](object);}}that['\u0073\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'](objectData);},async analysisTemplateData(){try{var _0xc4ae2a=(862027^862018)+(286444^286440);const that=this;_0xc4ae2a=(549394^549392)+(741212^741204);let barcodeObject={};that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']={};that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']='';var _0x2fd;let objectList=that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();_0x2fd='\u0068\u006C\u0062\u006C\u0067\u006F';that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=objectList['\u0057\u0069\u0064\u0074\u0068'];that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=objectList['\u0048\u0065\u0069\u0067\u0068\u0074'];var _0x623d;let drawObjects=objectList['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073'];_0x623d=(588192^588193)+(742046^742047);for(let i in drawObjects){if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="\u0042\u0041\u0052\u0043\u004F\u0044\u0045"){barcodeObject=drawObjects[i];}else if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="EDOCRQ".split("").reverse().join("")){that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']=drawObjects[i];}else if(drawObjects[i]['\u0046\u006F\u0072\u006D\u0061\u0074']=="EGAMI".split("").reverse().join("")){that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=drawObjects[i]['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'];}}if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']&&Object['\u006B\u0065\u0079\u0073'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074'])['\u006C\u0065\u006E\u0067\u0074\u0068']>(432956^432956)){that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']=that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];}if(barcodeObject&&Object['\u006B\u0065\u0079\u0073'](barcodeObject)['\u006C\u0065\u006E\u0067\u0074\u0068']>(810272^810272)&&that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']){const barCodeRes=await barCodeUtils['\u0067\u0065\u0074\u0042\u0061\u0072\u0063\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061'](barcodeObject,that['\u0063\u0061\u006E\u0076\u0061\u0073\u0042\u0061\u0072\u0043\u006F\u0064\u0065']);that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']=barCodeRes['\u0077\u0069\u0064\u0074\u0068'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=barCodeRes['\u0068\u0065\u0069\u0067\u0068\u0074'];let canvasId=barCodeRes['\u0063\u0061\u006E\u0076\u0061\u0073'];var _0x3f67a;const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'](canvasId);_0x3f67a=(593204^593205)+(242200^242201);that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0070\u0061\u0074\u0068']=filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];console['\u006C\u006F\u0067']("\u6761\u5F62\u7801\u8DEF\u5F84",that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0070\u0061\u0074\u0068']);that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0077\u0069\u0064\u0074\u0068']=barCodeRes['\u0077\u0069\u0064\u0074\u0068'];that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0068\u0065\u0069\u0067\u0068\u0074']=barCodeRes['\u0068\u0065\u0069\u0067\u0068\u0074'];}if(bleTool['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectList['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])||bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](objectList['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){objectList['\u004D\u0061\u0072\u0067\u0069\u006E']=810915^810913;if(objectList&&objectList['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']&&objectList['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(322901^322902)){var _0xecd4ad=(539522^539521)+(795533^795534);let maxWidth=objectList['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0xecd4ad="ppikne".split("").reverse().join("");for(const textData of objectList['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073']){if(textData['\u0046\u006F\u0072\u006D\u0061\u0074']="TXET".split("").reverse().join("")){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0073\u0065\u0074\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065'](textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);const metrics=that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);var _0x3aadad;const currentWidth=metrics['\u0077\u0069\u0064\u0074\u0068'];_0x3aadad=(348377^348381)+(451159^451156);if(currentWidth>maxWidth){maxWidth=currentWidth;}textData['\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(840412^840415));}}objectList['\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(371979^371976));that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=Math['\u0063\u0065\u0069\u006C'](maxWidth/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(633438^633437));}}that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];return objectList;}catch(error){throw error;}},imageData(){var _0x627d=(670465^670471)+(951507^951508);const that=this;_0x627d='\u0068\u006F\u0071\u0065\u0070\u0066';var _0xe609a;let imageListAll=that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();_0xe609a=274260^274256;that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']=imageListAll['\u0057\u0069\u0064\u0074\u0068'];that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']=imageListAll['\u0048\u0065\u0069\u0067\u0068\u0074'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']=that['\u0074\u0065\u006D\u0070\u006C\u0061\u0074\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']=imageListAll;that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']['\u004D\u0061\u0072\u0067\u0069\u006E']=182720^182722;return{"myCanvasRGBA":that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],'\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'],"printNum":that['\u0069\u006D\u0061\u0067\u0065\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']};},async qrCodeData(objectList){var _0xg1e5c;const that=this;_0xg1e5c=(827229^827229)+(479307^479304);that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']=objectList;let qrFilePath='';if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']&&Object['\u006B\u0065\u0079\u0073'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074'])['\u006C\u0065\u006E\u0067\u0074\u0068']>(222203^222203)&&that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']){qrFilePath=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0051\u0072\u0043\u006F\u0064\u0065']();}console['\u006C\u006F\u0067']("\u0071\u0072\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068",qrFilePath);console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068",that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']);if(that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']){const downLoadFileRes=await that['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']);that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=downLoadFileRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];}return{'\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041':that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],'\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'],'\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061':that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'],"printNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068'],"qrFilePath":qrFilePath,'\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068':that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'],"printType":that['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']};},async canvasQrCode(){var _0xcbe=(680154^680147)+(333888^333895);const that=this;_0xcbe=(270858^270858)+(400877^400872);return new Promise((resolve,reject)=>{try{var _0x6eca4a=(664816^664816)+(543670^543678);const query=wx['\u0063\u0072\u0065\u0061\u0074\u0065\u0053\u0065\u006C\u0065\u0063\u0074\u006F\u0072\u0051\u0075\u0065\u0072\u0079']();_0x6eca4a=(676029^676021)+(119314^119312);query['\u0073\u0065\u006C\u0065\u0063\u0074']("\u0023\u0071\u0072\u0043\u006F\u0064\u0065")['\u0066\u0069\u0065\u006C\u0064\u0073']({"node":!![],"size":!![]})['\u0065\u0078\u0065\u0063'](async res=>{try{var canvas=res[371940^371940]['\u006E\u006F\u0064\u0065'];await drawQrcode({'\u0063\u0061\u006E\u0076\u0061\u0073':canvas,'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':'qrCode',"width":that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"height":that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"text":that['\u0071\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']});await new Promise(resolve=>setTimeout(resolve,947023^947161));var _0x2bfc7b=(632373^632375)+(391332^391333);const qrfilePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068\u0051\u0072\u0043\u006F\u0064\u0065\u0054\u0077\u006F'](canvas);_0x2bfc7b='\u0063\u0064\u0070\u0067\u0065\u006E';console['\u006C\u006F\u0067']("\u0071\u0072\u0066\u0069\u006C\u0065\u0050\u0061\u0074\u0068\u0052\u0065\u0073",qrfilePathRes);let qrFilePath=qrfilePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];resolve(qrFilePath);}catch(error){reject(error);}});}catch(error){throw error;}});},downloadFile(url){try{return constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'],{'\u0075\u0072\u006C':url});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0031'],error);}},canvasToTempFilePathQrCode(canvas){try{return constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],{'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':"\u0071\u0072\u0043\u006F\u0064\u0065",'\u0063\u0061\u006E\u0076\u0061\u0073':canvas,'\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':'png','\u0071\u0075\u0061\u006C\u0069\u0074\u0079':1.0});}catch(error){console['\u006C\u006F\u0067']("\u5236\u7ED8\u65B0\u91CD\u8D25\u5931\u7801\u7EF4\u4E8C\u5236\u7ED8\u6B21\u4E8C\u7B2C".split("").reverse().join(""));throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0030'],error);}},canvasToTempFilePathQrCodeTwo(canvas){return new Promise((resolve,reject)=>{wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']({'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':"\u0071\u0072\u0043\u006F\u0064\u0065",'\u0063\u0061\u006E\u0076\u0061\u0073':canvas,'\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':"\u0070\u006E\u0067","quality":1.0,'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>{console['\u006C\u006F\u0067']("\u529F\u6210\u7247\u56FE\u7801\u7EF4\u4E8C\u6210\u751F\u6B21\u4E00\u7B2C".split("").reverse().join(""),res);resolve(res);},"fail":error=>{console['\u006C\u006F\u0067']("\u7B2C\u4E00\u6B21\u751F\u6210\u4E8C\u7EF4\u7801\u56FE\u7247\u5931\u8D25",error);resolve(this['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068\u0051\u0072\u0043\u006F\u0064\u0065'](canvas));}},this);});},canvasToTempFilePath(canvasId){return new Promise((resolve,reject)=>{wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']({"canvas":canvasId,'\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':"\u0070\u006E\u0067",'\u0071\u0075\u0061\u006C\u0069\u0074\u0079':1.0,'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>{console['\u006C\u006F\u0067']("\u751F\u6210\u6761\u5F62\u7801\u56FE\u7247\u6210\u529F",res);resolve(res);},'\u0066\u0061\u0069\u006C':error=>{console['\u006C\u006F\u0067']("\u8D25\u5931\u7247\u56FE\u7801\u5F62\u6761\u6210\u751F".split("").reverse().join(""),error);resolve(this['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068\u0054\u0077\u006F'](canvasId));}},this);});},canvasToTempFilePathTwo(canvasId){try{return constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],{"canvas":canvasId,'\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':"\u0070\u006E\u0067","quality":1.0});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0039'],error);}},getImageAllNum(){return this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'];},setImageAllNum(nObjectData){var _0x92624d;let objectData=nObjectData;_0x92624d=(362588^362580)+(686448^686457);this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']=868303^868303;for(let data of objectData){this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']=Number(this['\u0069\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D'])+Number(data['\u0043\u006F\u0070\u0069\u0065\u0073']);}},cleanBarcodeData(){this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0077\u0069\u0064\u0074\u0068']='';this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0068\u0065\u0069\u0067\u0068\u0074']='';this['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061']['\u0070\u0061\u0074\u0068']='';},cleanDataMeasure(){this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0077\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0068\u0065\u0069\u0067\u0068\u0074']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0057\u0069\u0064\u0074\u0068']='';this['\u0064\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']['\u0071\u0072\u0063\u006F\u0064\u0065\u0048\u0065\u0069\u0067\u0068\u0074']='';},cleanData(){var _0x9113ee;const that=this;_0x9113ee=(427948^427948)+(565829^565837);canvasDataRGBAUtils['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']();imageEncodeUtilsT50Pro['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsMp50['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsG15['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();imageEncodeUtilsG21['\u0063\u006C\u0065\u0061\u006E\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']();that['\u0063\u006C\u0065\u0061\u006E\u0042\u0061\u0072\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061']();that['\u0063\u006C\u0065\u0061\u006E\u0044\u0061\u0074\u0061\u004D\u0065\u0061\u0073\u0075\u0072\u0065']();},printCallback(nCallBack,nType){var _0xac29c;let callBack=nCallBack;_0xac29c='\u0064\u006A\u0066\u006F\u0066\u006D';let type=nType;supPrintUtils['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsMP50['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsG15['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);SupVanPrintUtilsG21['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsT50Pro['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsMp50['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsG15['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);imageEncodeUtilsG21['\u0070\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);canvasDataRGBAUtils['\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callBack,type);},async ConsumableInformation(){return new Promise(resolve=>{supPrintUtils['\u0072\u0065\u0061\u0064\u0043\u006F\u006E\u0073\u0075\u006D\u0061\u0062\u006C\u0065\u004D\u0065\u0073\u0073\u0061\u0067\u0065'](result=>{resolve(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](result));});});}};