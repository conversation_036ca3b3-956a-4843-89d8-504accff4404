import printingControl from"\u002E\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import supVanPrintUtilsG15 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";export default{'\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068':25,"matLength":2,'\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074':30,"dpiValue":8,"imageRgbaData":[],'\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074':[],'\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':0,"imageDataListAll":[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074':0,'\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065':false,'\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074':[],"objectData":null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'',"callback":null,async initEncodeData(objectData,imageRgbaData){try{console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035",objectData);var _0x_0xd52;const that=this;_0x_0xd52=(676098^676098)+(512397^512393);that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?638752^638753:381494^381494;that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},async initializeData(){var _0x3d_0x05d;const that=this;_0x3d_0x05d=823044^823046;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();var _0x97bad=(734862^734859)+(321168^321177);let length=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068'];_0x97bad=(737753^737755)+(467323^467315);that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-(460070^460066);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(216379^216376)){that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*(885394^885392);}console['\u006C\u006F\u0067']("htgneLtam".split("").reverse().join(""),that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(354926^354926)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=667220^667221;}var _0x6536f=(824055^824053)+(163715^163722);let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(312069^312071))*(689765^689727);_0x6536f=356492^356489;var _0x3f7e;let object={'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],"Width":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"Height":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065':rotateAngle,'\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(689036^689032),"VerticalNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(867334^867330)+1.5*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"OverturnType":that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],'\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};_0x3f7e=(971686^971694)+(717726^717718);imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{var _0x339dac;const that=this;_0x339dac=(793933^793933)+(459531^459523);let _bufLength=455141^451045;var _0xe2703g;let isEndFlag=!![];_0xe2703g=(129026^129028)+(511261^511252);let imgTotalCount=142435^142434;var _0xbda04f=(160104^160110)+(785496^785502);let marginleft=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']();_0xbda04f=294329^294334;var _0xd532a;let marginright=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']();_0xd532a='\u0069\u0069\u006A\u0064\u0063\u0061';console['\u006C\u006F\u0067']("\u56FE\u7247\u5DE6\u8FB9\u8DDD",marginleft);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft;console['\u006C\u006F\u0067']("\u6570\u5217\u7247\u56FE".split("").reverse().join(""),_nColumnTotalCnt);let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(341392^341399))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u6BCF\u5217\u5B57\u8282\u6570",nBytePerLine);let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(771564^771578))/nBytePerLine);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u7F13\u51B2\u533A\u6700\u5927\u5217\u6570",nMax);var _0xg_0xf44=(518375^518368)+(294111^294108);let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(903647^903646))/nMax);_0xg_0xf44=(689111^689105)+(653838^653830);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u56FE\u7247\u7F13\u51B2\u533A\u6570\u91CF\u0020\u0062\u0075\u0066\u0066\u0065\u0072\u0043\u006F\u0075\u006E\u0074\u0049\u006D\u0061\u0067\u0065",bufferCountImage);let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();console['\u006C\u006F\u0067']("\u0061\u006C\u006C\u0062\u0079\u0074\u0065\u0073\u957F\u5EA6",allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=606873^606873;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=452549^452549;i<bufferCountImage;i++){let _btBuf=new Uint8Array(_bufLength);pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u0075\u0074\u0054\u0079\u0070\u0065']);pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](439562^439563);pr['\u0073\u0065\u0074\u0053\u0061\u0076\u0065\u0070\u0061\u0070\u0065\u0072'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0053\u0061\u0076\u0065\u0050\u0061\u0070\u0065\u0072']?182258^182259:973769^973769);pr['\u0073\u0065\u0074\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==null&&that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==undefined?that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']:934189^934189);if(i==(349509^349509)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](436872^436873);}let bufferColumnCnt=859010^859010;if(i==bufferCountImage-(789833^789832)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](223883^223882);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](603609^603608);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;let end=star+bufferColumnCnt*nBytePerLine;var _0x2a014e;let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_0x2a014e=(691677^691669)+(991834^991834);_btBuf[638146^638150]=bufferColumnCnt&(625175^625384);_btBuf[932438^932435]=bufferColumnCnt>>(740055^740063)&(272898^273149);for(var y=338238^338238;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(210355^210365)+y]=b[y];}let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](717195^717197);for(var z=824303^824301;z<(726143^726139);z++){_btBuf[z]=btdata[z-(971742^971740)];}_btBuf[710044^710042]=nBytePerLine&(663051^663284);var _0x631c;let left=913634^913634;_0x631c='\u0063\u006C\u006D\u0067\u006D\u0062';if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']!=(735609^735609)){left=950412^950413;}if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(144131^144128)){let marginLeft=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']+left)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];let marginRight=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_btBuf[598157^598149]=marginLeft&(318385^318286);_btBuf[596485^596492]=marginLeft>>(588114^588122)&(702334^702337);_btBuf[133992^133986]=marginRight&(270403^270524);_btBuf[289794^289801]=marginRight>>(285430^285438)&(210634^210485);}else{let marginLeft=((that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(412542^412538))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(511823^511821)+left*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];let marginRight=(that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(835689^835693))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginLeft;let margin=Math['\u006D\u0061\u0078'](631968^631972,marginLeft);_btBuf[299193^299185]=margin&(472367^472528);_btBuf[377011^377018]=margin>>(100014^100006)&(258731^258644);margin=Math['\u006D\u0061\u0078'](326040^326044,marginRight);_btBuf[455976^455970]=margin&(411059^410956);_btBuf[874976^874987]=margin>>(906361^906353)&(619157^619114);}_btBuf[390103^390107]=884831^884831;_btBuf[459665^459676]=770048^770048;let len=_btBuf[374047^374042];len<<=658467^658475;len+=_btBuf[955287^955283];len*=_btBuf[575572^575570];len+=813030^813032;var _0x8953e=(761141^761138)+(777519^777517);let un=996577^996577;_0x8953e=695627^695618;for(var j=677534^677532;j<(692597^692603);j++){un+=_btBuf[j];}var _0xad55fe;let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(208456^208712));_0xad55fe=664823^664823;if(x>(883498^883498)){for(var k=368006^368006;k<x;k++){un+=_btBuf[(k+(153616^153617))*(768646^768902)-(761253^761252)];}}_btBuf[121097^121097]=un;_btBuf[108641^108640]=un>>(367334^367342);let sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](_btBuf);if(sendData){that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}console['\u006C\u006F\u0067']("\u0070\u0072",pr);}}}catch(error){throw error;}},async doPrint(){const that=this;console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u957F\u5EA6",that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']);console['\u006C\u006F\u0067']("\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D\u0028\u0029\u957F\u5EA6",bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']());if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(555584^555584)){supVanPrintUtilsG15['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u7A7A\u4E3A\u80FD\u4E0D\u636E\u6570\u6A21\u5B57\u5370\u6253".split("").reverse().join("")));}}else{supVanPrintUtilsG15['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=215891^215891;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};