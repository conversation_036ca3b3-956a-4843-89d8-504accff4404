import MSTA_REG from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import FSTA_REG from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";export default{'\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072':0,"labelNoMore":0,'\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072':0,'\u006C\u006F\u0077\u0042\u0061\u0074\u0074\u0065\u0072\u0079':0,"deviceBusy":0,"notInstalled":0,"headTempHigh":0,"loamCakeOpen":0,'\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E':0,"secondDeviceBusy":0,'\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064':0,'\u0063\u0068\u0061\u0072\u0067\u0069\u006E\u0067':0,'\u0069\u006E\u0073\u0065\u0072\u0074\u0055\u0053\u0042':0,'\u0076\u0061\u006C\u0042\u0061\u0074\u0074\u0065\u0072\u0079':0,"bufSta":0,"MSTA_REG":MSTA_REG,"FSTA_REG":FSTA_REG,'\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074':0,"changeDpi":8,Refresh(b){try{MSTA_REG['\u0042\u0075\u0066\u0053\u0074\u0061']=(b[664614^664614]&(679539^679538))>(153617^153617)?461348^461349:175270^175270;MSTA_REG['\u004C\u0061\u0062\u0052\u0077\u0045\u0072\u0072']=(b[694098^694098]&(379083^379081))>(125749^125749)?614681^614680:657977^657977;MSTA_REG['\u004C\u0061\u0062\u0045\u006E\u0064']=(b[323362^323362]&(189008^189012))>(390561^390561)?310617^310616:244711^244711;MSTA_REG['\u004C\u0061\u0062\u0058\u0068\u0045\u0072\u0072']=(b[743610^743610]&(791182^791174))>(688378^688378)?960347^960346:730107^730107;MSTA_REG['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']=(b[868803^868803]&(943304^943320))>(387087^387087)?659819^659818:117427^117427;MSTA_REG['\u0052\u0069\u0062\u0045\u006E\u0064']=(b[377013^377013]&(559185^559217))>(252389^252389)?136822^136823:567208^567208;MSTA_REG['\u0052\u0069\u0062\u0058\u0068\u0045\u0072\u0072']=(b[917383^917383]&(467925^467861))>(152123^152123)?298618^298619:727342^727342;MSTA_REG['\u0043\u0068\u006B\u004D\u0061\u0074\u004F\u006B']=(b[590758^590758]&(838800^838672))>(653632^653632)?574845^574844:402550^402550;MSTA_REG['\u0042\u0061\u0074\u004C\u006F\u0077']=(b[548172^548172]&(506288^506352))>(920939^920939)?539251^539250:757506^757506;MSTA_REG['\u0053\u0079\u0073\u0045\u0072\u0072']=(b[396140^396141]&(812982^812981))>(940759^940759)?592788^592789:256028^256028;MSTA_REG['\u0043\u006F\u006D\u0045\u0078\u0065\u0053\u0074\u0061']=(b[471659^471658]&(671143^671139))>(318852^318852)?695368^695369:218260^218260;MSTA_REG['\u0043\u0075\u0074\u004E\u0065\u0065\u0064\u0043\u006C\u0072']=(b[328907^328906]&(121224^121216))>(935087^935087)?115334^115335:252799^252799;MSTA_REG['\u0044\u0065\u0076\u0043\u006C\u0072\u0053\u0074\u0061']=(b[130776^130777]&(309801^309817))>(143465^143465)?756980^756981:513105^513105;FSTA_REG['\u0066\u0042\u0031\u0053\u0074\u0061']=(b[149843^149841]&(264513^264512))>(514695^514695)?430636^430637:225481^225481;FSTA_REG['\u0066\u0042\u0032\u0053\u0074\u0061']=(b[840287^840285]&(210200^210202))>(988962^988962)?323439^323438:433529^433529;FSTA_REG['\u0066\u0042\u0033\u0053\u0074\u0061']=(b[546934^546932]&(765324^765320))>(128249^128249)?351744^351745:224446^224446;FSTA_REG['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']=(b[332378^332376]&(339954^339962))>(628924^628924)?532882^532883:436627^436627;FSTA_REG['\u0052\u0069\u0062\u0045\u006E\u0064']=(b[591834^591832]&(190779^190763))>(580332^580332)?866658^866659:823203^823203;FSTA_REG['\u004C\u0061\u0062\u0045\u006E\u0064']=(b[367844^367846]&(930160^930128))>(172483^172483)?490878^490879:399716^399716;FSTA_REG['\u0050\u0072\u0074\u0053\u0074\u0061']=(b[588907^588905]&(605409^605345))>(579421^579421)?410576^410577:130298^130298;FSTA_REG['\u0073\u0044\u0065\u0076\u0042\u0075\u0073\u0079']=(b[845067^845065]&(638799^638927))>(889622^889622)?466602^466603:141510^141510;FSTA_REG['\u004C\u0061\u0062\u0046\u0069\u0078\u0045\u0072\u0072']=(b[363429^363430]&(650240^650241))>(628387^628387)?197598^197599:188632^188632;FSTA_REG['\u0071\u0043\u0075\u0074\u0045\u0072\u0072']=(b[438958^438957]&(751827^751825))>(457157^457157)?497653^497652:875944^875944;FSTA_REG['\u0053\u0054\u0075\u0062\u0065\u0046\u0069\u0078\u0045\u0072\u0072']=(b[149763^149760]&(849393^849397))>(787294^787294)?658935^658934:584460^584460;FSTA_REG['\u0053\u0054\u0075\u0062\u0065\u0045\u006E\u0064']=(b[177022^177021]&(880431^880423))>(107004^107004)?182764^182765:315735^315735;FSTA_REG['\u0062\u0043\u0075\u0074\u0045\u0072\u0072']=(b[255306^255305]&(770598^770614))>(512754^512754)?405185^405184:627392^627392;FSTA_REG['\u0052\u0069\u0062\u0046\u0069\u0078\u0045\u0072\u0072']=(b[139800^139803]&(740301^740333))>(169201^169201)?302094^302095:542550^542550;this['\u0062\u0075\u0066\u0053\u0074\u0061']=(b[796431^796431]&(640380^640381))>(196138^196138)?355055^355054:792851^792851;this['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']=(b[418706^418706]&(724641^724643))>(793760^793760)?900585^900584:560067^560067;this['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']=(b[355638^355638]&(300860^300856))>(251071^251071)?137420^137421:544355^544355;this['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']=(b[981001^981001]&(425711^425703))>(589689^589689)?223029^223028:963100^963100;this['\u006C\u006F\u0077\u0042\u0061\u0074\u0074\u0065\u0072\u0079']=(b[489615^489615]&(691853^691917))>(866753^866753)?969451^969450:606397^606397;this['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']=(b[889785^889785]&(531233^531249))>(476493^476493)?482868^482869:815085^815085;this['\u0068\u0065\u0061\u0064\u0054\u0065\u006D\u0070\u0048\u0069\u0067\u0068']=(b[266478^266479]&(795611^795603))>(347773^347773)?202106^202107:115777^115777;this['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']=(b[812463^812462]&(197738^197742))>(637077^637077)?190075^190074:491516^491516;this['\u006C\u006F\u0061\u006D\u0043\u0061\u006B\u0065\u004F\u0070\u0065\u006E']=(b[911145^911147]&(848278^848286))>(205524^205524)?453667^453666:402021^402021;this['\u0069\u006E\u0073\u0065\u0072\u0074\u0055\u0053\u0042']=(b[885901^885903]&(956695^956679))>(946008^946008)?942692^942693:632846^632846;this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']=(b[898382^898380]&(475143^475207))>(657045^657045)?209970^209971:226131^226131;this['\u0073\u0065\u0063\u006F\u006E\u0064\u0044\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']=(b[924768^924770]&(695439^695311))>(251756^251756)?728789^728788:543748^543748;this['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']=(b[787972^787975]&(417054^417055))>(149886^149886)?143955^143954:186476^186476;this['\u0063\u0068\u0061\u0072\u0067\u0069\u006E\u0067']=(b[320017^320018]&(163467^163339))>(350864^350864)?246066^246067:779048^779048;this['\u0063\u0068\u0061\u006E\u0067\u0065\u0044\u0070\u0069']=b[290668^290666]+(b[594280^594287]<<(843356^843348));if(this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(245100^245101)){this['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074']=b[819798^819795];this['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074']<<=384835^384843;this['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074']+=b[262457^262461];}}catch(e){}}};