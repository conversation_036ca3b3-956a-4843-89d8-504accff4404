/**
 * 在线商城配置文件
 * 请根据实际情况修改以下配置
 */

const onlineStoreConfig = {
  // ==================== 基础配置 ====================
  
  // 目标小程序AppID - 必填
  // 请替换为实际的在线商城小程序AppID
  appId: 'wx898834a20ceeb879',
  
  // 目标小程序页面路径 - 必填
  // 例如: 'pages/index/index' 或 'pages/store/store'
  path: 'pages/index/index-page',
  
  // 环境版本 - 可选
  // develop: 开发版
  // trial: 体验版  
  // release: 正式版（默认）
  envVersion: 'release',
  
  // ==================== 调试配置 ====================
  
  // 是否启用调试模式
  debug: false,
  
  // ==================== 传递参数配置 ====================
  
  // 是否传递设备信息
  passDeviceInfo: false,
  
  // 是否传递标签内容
  passLabelContent: false,
  
  // 是否传递模板信息
  passTemplateInfo: false,
  
  // 自定义业务参数
  customParams: {
    // 示例参数，请根据实际业务需要修改
    // companyId: 'your_company_id',
    // channelId: 'printer_channel',
    // version: '1.0.0'
  },
  
  // ==================== 错误处理配置 ====================
  
  // 错误提示文案
  errorMessages: {
    configNotSet: '请先配置目标小程序AppID',
    sceneNotSupported: '当前场景不支持跳转小程序，请在微信中打开',
    appNotExist: '目标小程序不存在或未发布，请检查AppID配置',
    pathNotExist: '目标页面不存在，请检查页面路径配置',
    defaultError: '无法打开在线商城，请稍后重试'
  }
};

module.exports = onlineStoreConfig;
