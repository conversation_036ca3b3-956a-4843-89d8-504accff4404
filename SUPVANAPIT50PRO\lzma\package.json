{"author": "<PERSON> <<EMAIL>>", "name": "lzma", "description": "A JavaScript implementation of the Lempel-Ziv-Markov (LZMA) chain compression algorithm", "version": "2.3.2", "homepage": "http://nmrugg.github.com/LZMA-JS/", "repository": {"type": "git", "url": "git://github.com/nmrugg/LZMA-JS.git"}, "bugs": {"url": "https://github.com/nmrugg/LZMA-JS/issues"}, "bin": {"lzma.js": "bin/lzma.js"}, "devDependencies": {"uglify-js": "=2.4.16"}, "scripts": {"test": "node test/test-node.js && node test/test-node.js --sync"}, "license": "MIT", "tonicExampleFilename": "demos/tonic_example.js", "keywords": ["lzma", "compression", "decompression"]}