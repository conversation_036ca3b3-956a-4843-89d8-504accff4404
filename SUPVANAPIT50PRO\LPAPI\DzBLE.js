var _0x50d9=['push','0000180A','characteristics','1F770088','00002A24','1F80017F881F840100881F80018088','1F78010288','width','getBluetoothDevices','onBLECharacteristicValueChange','5682904137','replace','onBluetoothAdapterStateChange','stopBluetoothDevicesDiscovery','1F710088','charCodeAt','properties','获取特征值失败','1F78010388','startBluetoothDevicesDiscovery','buffer','map','MT_DF1','fromCharCode','1F720088','pop','log','prototype','closeBLEConnection','00001800','value','0000FF03','蓝牙发送长度','join','connected','readBLECharacteristicValue','services','substring','platform','exec','toUpperCase','notifyBLECharacteristicValueChange','softFlags','writeBLECharacteristicValue','write\x20\x20\x20\x20\x20','length','charAt','000018F0','match','uuid','indexOf','F000FFC0'];var _0x4785=function(_0x50d9bd,_0x4785bb){_0x50d9bd=_0x50d9bd-0x0;var _0x446011=_0x50d9[_0x50d9bd];return _0x446011;};const sDeTongCheckSum=_0x4785('0xa');var maxSendDataLength=0x78;var sendDataWaitTime=0x14;var connectedDeviceId='';var connectedDevicServiceId='';var deviceInfoServiceId='';var writeCharacteristicsId='';var notifyCharacteristicsId='';var interval=null;var printList=[];var supportPrefixKeys=[];var printHex='';var sDidSendDataHandler;var sDidSendHexHandler;var sDidConnectedHandler;var currentPrinter={'name':'','DPI':0xcb,'width':0x180,'softFlags':0x0};module['exports']={'setSupportPrefixs':setSupportPrefixs,'startScanPeripherals':startScanPeripherals,'stopScanPeripherals':stopScanPeripherals,'connectPeripheral':connectPeripheral,'disconnectPeripheral':disconnectPeripheral,'scanedPeripherals':scanedPeripherals,'sendData':sendData,'currentConnectedDeviceId':currentConnectedDeviceId,'currentPrinterInfo':currentPrinterInfo,'setCurrentPrinterInfoProperty':setCurrentPrinterInfoProperty,'refreshScanPeripherals':refreshScanPeripherals};function currentPrinterInfo(){return currentPrinter;}function setCurrentPrinterInfoProperty(_0x383aa0,_0x1356c4){currentPrinter[_0x383aa0]=_0x1356c4;}function setSupportPrefixs(_0x35ee56){supportPrefixKeys=_0x35ee56;}function startScanPeripherals(_0x1e2c66){wx['closeBluetoothAdapter']({'complete':_0x5b7438=>{wx['openBluetoothAdapter']({'success':function(_0x3aaf2f){wx[_0x4785('0x13')]({'success':function(_0x3f5706){setTimeout(function(){scanedPeripherals(function(_0x585468){if(_0x1e2c66){_0x1e2c66(_0x585468);}});},0x7d0);}});wx[_0x4785('0xc')](function(_0x1a7369){});}});}});}function refreshScanPeripherals(_0x36947b){scanedPeripherals(function(_0x42bc28){if(_0x36947b){_0x36947b(_0x42bc28);}});}function currentConnectedDeviceId(){return connectedDeviceId?connectedDeviceId:'';}function stopScanPeripherals(){wx[_0x4785('0xd')]({'success':function(_0xd92c3f){}});}function connectPeripheral(_0x462ddc,_0x263831,_0x5d4ac4){sDidConnectedHandler=_0x263831;wx['onBLEConnectionStateChange'](function(_0x1c23be){if(_0x1c23be['deviceId']!=connectedDeviceId){return;}if(!_0x1c23be[_0x4785('0x22')]){if(_0x5d4ac4){_0x5d4ac4();}}});wx[_0x4785('0x9')](function(_0x3e0115){if(_0x3e0115['deviceId']!=connectedDeviceId){return;}if(_0x3e0115['characteristicId'][_0x4785('0x32')](_0x4785('0x4'))==0x0){parseDeviceInfo(_0x3e0115);}else if(_0x3e0115['characteristicId']==notifyCharacteristicsId){var _0x29b4ad=new Uint8Array(_0x3e0115[_0x4785('0x1e')]);if(_0x29b4ad[_0x4785('0x2d')]>0x2&&_0x29b4ad[0x0]==0x1f){var _0x3390fa=[];var _0x3154a7=[];for(var _0x16620a=0x0;_0x16620a<_0x29b4ad[_0x4785('0x2d')];_0x16620a++){if(_0x29b4ad[_0x16620a]==0x1f){_0x3154a7=[];_0x3390fa[_0x4785('0x0')](_0x3154a7);}_0x3154a7[_0x4785('0x0')](_0x29b4ad[_0x16620a]);}for(var _0x16620a=0x0;_0x16620a<_0x3390fa[_0x4785('0x2d')];_0x16620a++){var _0x2f79ae=_0x3390fa[_0x16620a];if(_0x2f79ae[0x1]==0x71){parseDPI(_0x2f79ae);}else if(_0x2f79ae[0x1]==0x72){parsePrintWidth(_0x2f79ae);}else if(_0x2f79ae[0x1]==0x84){parseSoftwareFlags(_0x2f79ae);}else if(_0x2f79ae[0x1]==0x77){parseBufferSize(_0x2f79ae);}}}}});stopScanPeripherals();wx['createBLEConnection']({'deviceId':_0x462ddc,'success':function(_0x62f23d){connectedDeviceId=_0x462ddc;connectedDevicServiceId=null;deviceInfoServiceId=null;notifyCharacteristicsId=null;writeCharacteristicsId=null;wx['getBLEDeviceServices']({'deviceId':connectedDeviceId,'success':function(_0x5004c8){var _0x58a764=_0x5004c8[_0x4785('0x24')];for(var _0x53975a=0x0;_0x53975a<_0x58a764['length'];_0x53975a++){var _0x19edfa=_0x5004c8['services'][_0x53975a]['uuid'];if(connectedDevicServiceId==null&&_0x19edfa['indexOf'](_0x4785('0x33'))!=0x0&&_0x19edfa[_0x4785('0x32')](_0x4785('0x2f'))!=0x0&&_0x19edfa[_0x4785('0x32')](_0x4785('0x1d'))!=0x0&&_0x19edfa[_0x4785('0x32')](_0x4785('0x1'))!=0x0&&_0x19edfa['indexOf']('00001801')!=0x0){connectedDevicServiceId=_0x19edfa;}if(_0x19edfa[_0x4785('0x32')](_0x4785('0x1'))==0x0){deviceInfoServiceId=_0x19edfa;}}console['log']('Service\x20\x20'+connectedDevicServiceId);wx['getBLEDeviceCharacteristics']({'deviceId':connectedDeviceId,'serviceId':connectedDevicServiceId,'success':function(_0x36986b){for(var _0x47f7a6=0x0;_0x47f7a6<_0x36986b['characteristics'][_0x4785('0x2d')];_0x47f7a6++){if(_0x36986b[_0x4785('0x2')][_0x47f7a6][_0x4785('0x10')]['notify']){if(notifyCharacteristicsId==null&&_0x36986b['characteristics'][_0x47f7a6]['uuid']['indexOf'](_0x4785('0x1f'))!=0x0){notifyCharacteristicsId=_0x36986b[_0x4785('0x2')][_0x47f7a6][_0x4785('0x31')];}}if(writeCharacteristicsId==null&&_0x36986b[_0x4785('0x2')][_0x47f7a6]['properties']['write']){writeCharacteristicsId=_0x36986b[_0x4785('0x2')][_0x47f7a6][_0x4785('0x31')];}}console['log']('notify\x20\x20\x20\x20'+notifyCharacteristicsId);console[_0x4785('0x1a')](_0x4785('0x2c')+writeCharacteristicsId);wx[_0x4785('0x29')]({'deviceId':connectedDeviceId,'serviceId':connectedDevicServiceId,'characteristicId':notifyCharacteristicsId,'state':!![],'success':function(_0x1d19db){}});console[_0x4785('0x1a')]('info\x20\x20\x20\x20\x20\x20'+deviceInfoServiceId);wx['getBLEDeviceCharacteristics']({'deviceId':connectedDeviceId,'serviceId':deviceInfoServiceId,'success':function(_0x161dff){for(var _0x357673=0x0;_0x357673<_0x161dff[_0x4785('0x2')]['length'];_0x357673++){if(_0x161dff['characteristics'][_0x357673]['uuid'][_0x4785('0x32')]('00002A24')==0x0){wx[_0x4785('0x23')]({'characteristicId':_0x161dff['characteristics'][_0x357673][_0x4785('0x31')],'deviceId':connectedDeviceId,'serviceId':deviceInfoServiceId});break;}}},'fail':function(){console['log']('获取蓝牙模块信息失败');if(_0x5d4ac4){_0x5d4ac4();}},'complete':function(){}});},'fail':function(){console['log'](_0x4785('0x11'));if(_0x5d4ac4){_0x5d4ac4();}},'complete':function(){}});}});},'fail':function(_0x1be290){console[_0x4785('0x1a')]('连接蓝牙设备失败',_0x1be290['errCode'],_0x1be290['errMsg']);if(_0x5d4ac4){_0x5d4ac4();}},'complete':function(){}});}function parseDeviceInfo(_0x21d4a3){var _0x5a191b=bin2String(_0x21d4a3[_0x4785('0x1e')]);maxSendDataLength=0xb4;sendDataWaitTime=0x21;var _0x5c73e4=_0x5a191b['split']('-');if(_0x5c73e4['length']>0x0){var _0x5ef884=_0x5c73e4[_0x4785('0x19')]();_0x5ef884=_0x5ef884[_0x4785('0x28')]();if(_0x5ef884==_0x4785('0x16')){maxSendDataLength=0x78;sendDataWaitTime=0x14;}else if(_0x5ef884[_0x4785('0x32')]('DF')==0x0){_0x5ef884=_0x5ef884[_0x4785('0xb')]('DF','');var _0x57392f=parseInt(_0x5ef884,0x10);if(_0x57392f&0x1){maxSendDataLength=0xb4;sendDataWaitTime=0x12;}else if(_0x57392f&0x2){maxSendDataLength=0x78;sendDataWaitTime=0x14;}else if(_0x57392f&0x4){maxSendDataLength=0xb4;sendDataWaitTime=0xa;}}}console[_0x4785('0x1a')](_0x4785('0x20')+maxSendDataLength);checkDPI();}function parseDPI(_0xf2b793){var _0x1a6bb3=0x0;var _0x387cdd=_0xf2b793[0x2];if(_0x387cdd==0x1){_0x1a6bb3=_0xf2b793[0x3];}else{var _0x54dde0=_0xf2b793[0x3];var _0x530420=_0xf2b793[0x4];_0x1a6bb3=(_0x54dde0<<0x8)+_0x530420;}currentPrinter['DPI']=_0x1a6bb3;checkPrintWidth();}function parsePrintWidth(_0x564c85){var _0x27da65=_0x564c85[0x3];var _0x1faa1b=_0x564c85[0x4];var _0x384052=(_0x27da65<<0x8)+_0x1faa1b;currentPrinter[_0x4785('0x7')]=_0x384052;checkSoftwareFlags();}function parseSoftwareFlags(_0x5046d7){var _0x2cbb8a=0x0;if(_0x5046d7[_0x4785('0x2d')]>=0xc){var _0x32f746=_0x5046d7[0x7];var _0x104b2a=_0x5046d7[0x8];var _0x5a7c6e=_0x5046d7[0x9];var _0x191eb9=_0x5046d7[0xa];_0x2cbb8a=(_0x32f746<<0x18)+(_0x104b2a<<0x10)+(_0x5a7c6e<<0x8)+_0x191eb9;}else if(_0x5046d7['length']>=0x7){var _0x32f746=_0x5046d7[0x6];_0x2cbb8a=0x1|_0x32f746&0x10;}currentPrinter[_0x4785('0x2a')]=_0x2cbb8a;wx['getSystemInfo']({'success':function(_0x585ba9){if(_0x585ba9[_0x4785('0x26')]=='ios'){checkDeviceType(!![]);if(sDidConnectedHandler){sDidConnectedHandler();}}else if(_0x585ba9['platform']=='android'){checkDeviceType(![]);if(sDidConnectedHandler){sDidConnectedHandler();}wx['setBLEMTU']({'deviceId':connectedDeviceId,'mtu':0x200});}}});}function parseBufferSize(_0x46d020){var _0x42f468=0x0;var _0xdd3ce8=_0x46d020[0x3];if(_0xdd3ce8>=0xc0){var _0x5f39f5=_0x46d020[0x3];var _0x13edc0=_0x46d020[0x4];_0x42f468=(_0x5f39f5<<0x8)+_0x13edc0;}else{_0x42f468=_0xdd3ce8;}postDatasWithBuffer(_0x42f468*0x1f4);}function disconnectPeripheral(){wx[_0x4785('0x1c')]({'deviceId':connectedDeviceId,'success':function(_0x5b4cb1){}});}function scanedPeripherals(_0x1100f7){wx[_0x4785('0x8')]({'success':function(_0x3f19d7){var _0x1600f8=new Array();for(var _0x532424=0x0;_0x532424<_0x3f19d7['devices']['length'];_0x532424++){var _0x22a666=_0x3f19d7['devices'][_0x532424]['name'];if(isMyPrinterForName(_0x22a666)){_0x1600f8[_0x4785('0x0')](_0x3f19d7['devices'][_0x532424]);}}if(_0x1100f7){_0x1100f7(_0x1600f8);}}});}function sendData(_0xc7cf95,_0x555fb5){if(printList==null){printList=new Array();}if(_0xc7cf95!=null){printList['push'](_0xc7cf95);if(printList['length']>0x0){var _0x432f81=printList[0x0];if(_0x432f81[_0x4785('0x2d')]>0x0){sDidSendDataHandler=_0x555fb5;checkBufferSize();}else{clearInterval(interval);printList['splice'](0x0,0x1);if(_0x555fb5){_0x555fb5();}}}}}function postDatasWithBuffer(_0x565ea7){if(_0x565ea7<0x7d0){setTimeout(()=>{checkBufferSize();},0x32);}else{printHex='';var _0x1f8f00=printList[0x0];if(_0x1f8f00!=null){for(;_0x1f8f00[_0x4785('0x2d')]>0x0;_0x1f8f00['splice'](0x0,0x1)){var _0x509c19=_0x1f8f00[0x0];if(printHex['length']+_0x509c19['length']>=_0x565ea7){break;}printHex+=_0x509c19;}}postHexValue(function(){if(_0x1f8f00!=null&&_0x1f8f00[_0x4785('0x2d')]>0x0){checkBufferSize();}else{printList['splice'](0x0,0x1);if(sDidSendDataHandler){sDidSendDataHandler();}}});}}function postHexValue(_0x589827){sDidSendHexHandler=_0x589827;postHex();}function postHex(){if(printHex!=null&&printHex[_0x4785('0x2d')]>0x0){interval=setTimeout(function(){var _0x155b40;if(printHex!=null&&printHex['length']>maxSendDataLength){_0x155b40=printHex['substr'](0x0,maxSendDataLength);printHex=printHex['substring'](maxSendDataLength);}else{_0x155b40=printHex;printHex='';}if(_0x155b40&&_0x155b40['length']>0x0){var _0x16faa3=new Uint8Array(_0x155b40[_0x4785('0x30')](/[\da-f]{2}/gi)['map'](function(_0x30e275){return parseInt(_0x30e275,0x10);}));wx[_0x4785('0x2b')]({'deviceId':connectedDeviceId,'serviceId':connectedDevicServiceId,'characteristicId':writeCharacteristicsId,'value':_0x16faa3['buffer'],'success':function(_0x246c87){}});}postHex();},sendDataWaitTime);}else{if(sDidSendHexHandler){sDidSendHexHandler();}}}function checkDeviceType(_0x5aae98){if(_0x5aae98){sendCMD(_0x4785('0x12'));}else{sendCMD(_0x4785('0x6'));}}function checkDPI(){sendCMD(_0x4785('0xe'));}function checkPrintWidth(){sendCMD(_0x4785('0x18'));}function checkSoftwareFlags(){sendCMD(_0x4785('0x5'));}function checkBufferSize(){sendCMD(_0x4785('0x3'));}function sendCMD(_0x59fb6a){var _0x5c9761=_0x59fb6a;var _0x3bb2ce=new Uint8Array(_0x5c9761['match'](/[\da-f]{2}/gi)[_0x4785('0x15')](function(_0x373de6){return parseInt(_0x373de6,0x10);}));wx[_0x4785('0x2b')]({'deviceId':connectedDeviceId,'serviceId':connectedDevicServiceId,'characteristicId':writeCharacteristicsId,'value':_0x3bb2ce[_0x4785('0x14')],'success':function(_0x5126a9){}});}function ab2hex(_0x46f702){var _0xa31c12=Array[_0x4785('0x1b')]['map']['call'](new Uint8Array(_0x46f702),function(_0x499a03){return('00'+_0x499a03['toString'](0x10))['slice'](-0x2);});return _0xa31c12[_0x4785('0x21')](',');}function bin2String(_0x119702){var _0x45f6f9=new Uint8Array(_0x119702);var _0x4394af='';for(var _0x4b3830=0x0;_0x4b3830<_0x45f6f9[_0x4785('0x2d')];_0x4b3830++){_0x4394af+=String[_0x4785('0x17')](_0x45f6f9[_0x4b3830]);}return _0x4394af;}function isMyPrinterForName(_0x2abc8c){if(_0x2abc8c==null||_0x2abc8c['length']==0x0){return![];}var _0x574f62=_0x2abc8c[_0x4785('0x28')]();var _0x338205=_0x574f62['split']('-');if(_0x338205[_0x4785('0x2d')]>0x1){_0x574f62=_0x338205[_0x338205['length']-0x1];if(_0x574f62['length']<0x8){return![];}}else{return![];}var _0x33f940=new RegExp('^[A-Z]{0,2}[0-9]{4,5}[0-9A-Z]{2,5}[0-9]{2}$','g');if(!_0x33f940[_0x4785('0x27')](_0x574f62)){return![];}var _0x337b8e=new RegExp('^[0-9]+','g');var _0x4a9e00=_0x337b8e['exec'](_0x574f62);var _0x26c4f7='';var _0x1fdea7=0x0;if(_0x574f62['charAt'](0x1)<'0'||_0x574f62[_0x4785('0x2e')](0x1)>'9'){_0x26c4f7=_0x574f62['substring'](0x0,0x2);_0x574f62=_0x574f62['substring'](0x2,_0x574f62['length']);_0x1fdea7+=_0x26c4f7[_0x4785('0xf')](0x0)*0xb;_0x1fdea7+=_0x26c4f7['charCodeAt'](0x1)*0xc;}else if(_0x574f62['charAt'](0x0)<'0'||_0x574f62[_0x4785('0x2e')](0x0)>'9'){_0x26c4f7=_0x574f62[_0x4785('0x25')](0x0,0x1);_0x574f62=_0x574f62['substring'](0x1,_0x574f62[_0x4785('0x2d')]);_0x1fdea7+=_0x26c4f7['charCodeAt'](0x0)*0x11;}if(_0x26c4f7[_0x4785('0x28')]()=='D'||_0x26c4f7['toUpperCase']()=='O'){}else if(_0x574f62[_0x4785('0x2d')]<0x8){return![];}else{if(supportPrefixKeys['length']>0x0){for(var _0x41abaf=0x0;_0x41abaf<supportPrefixKeys['length'];_0x41abaf++){var _0x26245d=supportPrefixKeys[_0x41abaf];if(_0x26245d[_0x4785('0x2d')]>0x0){var _0x706a16=_0x2abc8c['substring'](0x0,_0x26245d[_0x4785('0x2d')]);if(_0x706a16['toUpperCase']()==_0x26245d[_0x4785('0x28')]()){break;}else{return![];}}else{return![];}}}}if(!_0x4a9e00||_0x574f62[_0x4785('0x2d')]>=0x9||_0x574f62['charAt'](0x3)!='0'){if(_0x4a9e00){_0x1fdea7+=(_0x574f62[_0x4785('0x2e')](0x0)-'0')*0x2;_0x1fdea7+=(_0x574f62['charAt'](0x1)-'0')*0x3;_0x1fdea7+=(_0x574f62[_0x4785('0x2e')](0x2)-'0')*0x5;for(var _0x41abaf=0x4;_0x41abaf<_0x574f62['length'];++_0x41abaf){_0x1fdea7+=(_0x574f62['charAt'](_0x41abaf)-'0')*((_0x41abaf&0x1)==0x0?0x7:0x9);}}else{_0x1fdea7+=_0x574f62[_0x4785('0x2e')](0x0)*0x2;_0x1fdea7+=_0x574f62['charAt'](0x1)*0x3;_0x1fdea7+=_0x574f62[_0x4785('0x2e')](0x2)*0x5;for(var _0x41abaf=0x4;_0x41abaf<_0x574f62['length'];++_0x41abaf){_0x1fdea7+=_0x574f62[_0x4785('0xf')](_0x41abaf)*((_0x41abaf&0x1)==0x0?0x7:0x9);}}var _0x15b537=_0x1fdea7%0xa;if(sDeTongCheckSum[_0x4785('0x2e')](_0x15b537)!=_0x574f62['charAt'](0x3)){return![];}}return!![];}