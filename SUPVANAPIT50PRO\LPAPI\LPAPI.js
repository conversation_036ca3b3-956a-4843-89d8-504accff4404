var dzBLE = require('./DzBLE.js');
var bitmapPackage = require('./BitmapPackage.js');
var barcodeWriter = require('./BarcodeWriter.js');
const {
    currentPrinterInfo
} = require('./DzBLE.js');
var _0x14b5=['indexOf','currentPrinterInfo','strokeRect','length','#000000','#FFFFFF','refreshScanPeripherals','px\x20sans-serif','font','lineWidth','deviceName','name','sendData','measureText','createImage','getBLEDeviceServices','restore','save','setCurrentPrinterInfoProperty','currentConnectedDeviceId','fillRect','rotate','clearRect','startScanPeripherals','translate','DPI','toUpperCase','fillStyle','getImageData','disconnectPeripheral','connectPeripheral','arrayWithImage','data','sort','deviceId','services'];var _0xbd5c=function(_0x14b5d9,_0xbd5c6d){_0x14b5d9=_0x14b5d9-0x0;var _0x585620=_0x14b5[_0x14b5d9];return _0x585620;};var printWidth=0x0;var printHeight=0x0;var printOrientation=0x0;var itemOrientation=0x0;var itemHorizontalAlignment=0x0;var itemVerticalAlignment=0x0;var printPageGapType=-0x1;var printPageGapLength=0x2;var printDarkness=0x0;var printSpeed=0x0;var printGrayThreshold=0xc0;var canvas=null;var context=null;var isConnecting=![];function covert(_0x3cbebc){var _0x1df64e=_0x3cbebc*DPI()/25.4;_0x1df64e=_0x1df64e>=0x0?_0x1df64e+0.1:_0x1df64e-0.1;return parseInt(_0x1df64e);}function DPI(){var _0x40922e=currentPrinterInfo()['DPI'];return _0x40922e;}function setPrintPageGapType(_0x2318e8){printPageGapType=_0x2318e8;}function setPrintPageGapLength(_0x3f1f82){printPageGapLength=_0x3f1f82;}function setPrintDarkness(_0x4287d4){printDarkness=_0x4287d4;}function setPrintSpeed(_0x53f909){printSpeed=_0x53f909;}function setPrintGrayThreshold(_0x5bed87){printGrayThreshold=_0x5bed87;}function setSupportPrefixs(_0x1bc1f8){dzBLE['setSupportPrefixs'](_0x1bc1f8);}function connectingPrinterName(){return currentPrinterInfo()[_0xbd5c('0xb')];}function scanedPrinters(_0x34219f){dzBLE[_0xbd5c('0x17')](function(_0x161de6){if(_0x34219f){_0x34219f(_0x161de6);}});}function compare(_0x4ae4e6){return function(_0x5f486d,_0x31774f){var _0x1300a4=_0x5f486d[_0x4ae4e6];var _0x2a4d2c=_0x31774f[_0x4ae4e6];return _0x2a4d2c-_0x1300a4;};}function openPrinter(_0x52c8b4,_0x4763aa,_0xc09cd6){wx[_0xbd5c('0xf')]({'deviceId':dzBLE[_0xbd5c('0x13')](),'success':function(_0x132bc0){isConnecting=!![];if(_0x132bc0){if(_0x132bc0[_0xbd5c('0x22')]==null&&(_0x132bc0['services']==null||_0x132bc0[_0xbd5c('0x23')]['length']==0x0)){isConnecting=![];}}},'fail':function(_0x2e887e){isConnecting=![];},'complete':function(_0x490c3c){if(isConnecting){if(_0x4763aa){_0x4763aa(dzBLE[_0xbd5c('0x13')]());}return;}scanedPrinters(function(_0x536408){var _0x351b9d=selectedDevice(_0x536408,_0x52c8b4);if(_0x351b9d[_0xbd5c('0x22')]==null||_0x351b9d[_0xbd5c('0x22')][_0xbd5c('0x3')]==0x0){setTimeout(function(){dzBLE['refreshScanPeripherals'](function(_0x3269c6){var _0x2841f2=selectedDevice(_0x3269c6,_0x52c8b4);if(_0x2841f2['deviceId']==null||_0x2841f2['deviceId'][_0xbd5c('0x3')]==0x0){setTimeout(function(){dzBLE[_0xbd5c('0x6')](function(_0x4fc50f){var _0x4eade2=selectedDevice(_0x4fc50f,_0x52c8b4);if(_0x4eade2['deviceId']==null||_0x4eade2[_0xbd5c('0x22')][_0xbd5c('0x3')]==0x0){console['log']('没有发现打印机');dzBLE['setCurrentPrinterInfoProperty']('name','');if(_0xc09cd6){_0xc09cd6(0x1);}}else{connectDevice(_0x4eade2,_0x4763aa,_0xc09cd6);}});},0xfa0);}else{connectDevice(_0x2841f2,_0x4763aa,_0xc09cd6);}});},0xfa0);}else{connectDevice(_0x351b9d,_0x4763aa,_0xc09cd6);}});}});}function selectedDevice(_0x541d6f,_0x135cb1){var _0x4cc67d={'deviceId':null,'deviceName':null};if(_0x541d6f['length']>0x0){if(_0x135cb1&&_0x135cb1['length']>0x0){for(var _0x12ee2d=0x0;_0x12ee2d<_0x541d6f[_0xbd5c('0x3')];_0x12ee2d++){var _0x295755=_0x541d6f[_0x12ee2d];var _0x521d7c=_0x295755[_0xbd5c('0xb')]['substring'](0x0,_0x135cb1['length']);if(_0x521d7c[_0xbd5c('0x1a')]()[_0xbd5c('0x0')](_0x135cb1[_0xbd5c('0x1a')]())>=0x0){_0x4cc67d[_0xbd5c('0x22')]=_0x295755[_0xbd5c('0x22')];_0x4cc67d['deviceName']=_0x295755['name'];break;}}}if(_0x135cb1==null||_0x135cb1[_0xbd5c('0x3')]==0x0){_0x541d6f[_0xbd5c('0x21')](compare('RSSI'));_0x4cc67d['deviceId']=_0x541d6f[0x0][_0xbd5c('0x22')];_0x4cc67d[_0xbd5c('0xa')]=_0x541d6f[0x0][_0xbd5c('0xb')];}}return _0x4cc67d;}function connectDevice(_0x36715d,_0x201b45,_0x18de4d){dzBLE[_0xbd5c('0x12')](_0xbd5c('0xb'),_0x36715d[_0xbd5c('0xa')]);dzBLE[_0xbd5c('0x1e')](_0x36715d['deviceId'],function(){if(_0x201b45){_0x201b45(_0x36715d[_0xbd5c('0x22')]);}},function(){dzBLE[_0xbd5c('0x12')](_0xbd5c('0xb'),'');if(_0x18de4d){_0x18de4d(0x0);}});}function connectingPrinterDetailInfos(){return dzBLE[_0xbd5c('0x1')]();}function closePrinter(){dzBLE[_0xbd5c('0x1d')]();}function startDrawLabel(_0x3d8896,_0x101ffa,_0x5f5840){printOrientation=_0x5f5840;printWidth=covert(_0x3d8896);printHeight=covert(_0x101ffa);itemOrientation=0x0;itemHorizontalAlignment=0x0;itemVerticalAlignment=0x0;canvas=wx['createOffscreenCanvas']({'type':'2d','width':printWidth,'height':printHeight});if(canvas==null){return;}context=canvas['getContext']('2d');if(context==null){return;}context[_0xbd5c('0x16')](0x0,0x0,canvas['width'],canvas['height']);context[_0xbd5c('0x1b')]=_0xbd5c('0x5');context['fillRect'](0x0,0x0,printWidth+0x5,printHeight+0x5);context['fillStyle']=_0xbd5c('0x4');}function endDrawLabel(){return context[_0xbd5c('0x1c')](0x0,0x0,printWidth,printHeight);}function print(_0x1a4b10){const _0x2e4358=context[_0xbd5c('0x1c')](0x0,0x0,printWidth,printHeight)[_0xbd5c('0x20')];var _0x4bdf4=[];var _0x2af526=printWidth;var _0x5d7c62=printHeight;if(printOrientation==0x0){_0x4bdf4=_0x2e4358;}else if(printOrientation==0x5a){for(var _0x5362eb=0x0;_0x5362eb<_0x2af526;_0x5362eb++){for(var _0x224bcb=0x0;_0x224bcb<_0x5d7c62;_0x224bcb++){var _0x36cbba=(_0x5362eb*_0x5d7c62+_0x224bcb)*0x4;var _0x2e4c5d=((_0x5d7c62-0x1-_0x224bcb)*_0x2af526+_0x5362eb)*0x4;_0x4bdf4[_0x36cbba+0x0]=_0x2e4358[_0x2e4c5d+0x0];_0x4bdf4[_0x36cbba+0x1]=_0x2e4358[_0x2e4c5d+0x1];_0x4bdf4[_0x36cbba+0x2]=_0x2e4358[_0x2e4c5d+0x2];_0x4bdf4[_0x36cbba+0x3]=_0x2e4358[_0x2e4c5d+0x3];}}var _0x2af05e=_0x2af526;_0x2af526=_0x5d7c62;_0x5d7c62=_0x2af05e;}else if(printOrientation==0xb4){for(var _0x224bcb=0x0;_0x224bcb<_0x5d7c62;_0x224bcb++){for(var _0x5362eb=0x0;_0x5362eb<_0x2af526;_0x5362eb++){var _0x36cbba=(_0x224bcb*_0x2af526+_0x5362eb)*0x4;var _0x2e4c5d=((_0x5d7c62-0x1-_0x224bcb)*_0x2af526+_0x2af526-0x1-_0x5362eb)*0x4;_0x4bdf4[_0x36cbba+0x0]=_0x2e4358[_0x2e4c5d+0x0];_0x4bdf4[_0x36cbba+0x1]=_0x2e4358[_0x2e4c5d+0x1];_0x4bdf4[_0x36cbba+0x2]=_0x2e4358[_0x2e4c5d+0x2];_0x4bdf4[_0x36cbba+0x3]=_0x2e4358[_0x2e4c5d+0x3];}}}else if(printOrientation==0x10e){for(var _0x5362eb=0x0;_0x5362eb<_0x2af526;_0x5362eb++){for(var _0x224bcb=0x0;_0x224bcb<_0x5d7c62;_0x224bcb++){var _0x36cbba=(_0x5362eb*_0x5d7c62+_0x224bcb)*0x4;var _0x2e4c5d=(_0x224bcb*_0x2af526+_0x2af526-0x1-_0x5362eb)*0x4;_0x4bdf4[_0x36cbba+0x0]=_0x2e4358[_0x2e4c5d+0x0];_0x4bdf4[_0x36cbba+0x1]=_0x2e4358[_0x2e4c5d+0x1];_0x4bdf4[_0x36cbba+0x2]=_0x2e4358[_0x2e4c5d+0x2];_0x4bdf4[_0x36cbba+0x3]=_0x2e4358[_0x2e4c5d+0x3];}}var _0x2af05e=_0x2af526;_0x2af526=_0x5d7c62;_0x5d7c62=_0x2af05e;}var _0x155f7a=bitmapPackage[_0xbd5c('0x1f')](_0x4bdf4,currentPrinterInfo()[_0xbd5c('0x19')],currentPrinterInfo()['width'],_0x2af526,_0x5d7c62,printPageGapType,printPageGapLength*0x64,printDarkness,printSpeed,printGrayThreshold,!![],currentPrinterInfo()['softFlags']);dzBLE[_0xbd5c('0xc')](_0x155f7a,function(){if(_0x1a4b10){_0x1a4b10();}});}function setItemOrientation(_0xb36435){if(_0xb36435!=0x0&&_0xb36435!=0x5a&&_0xb36435!=0xb4&&_0xb36435!=0x10e){return;}itemOrientation=_0xb36435;}function setItemHorizontalAlignment(_0x59127e){if(_0x59127e!=0x0&&_0x59127e!=0x1&&_0x59127e!=0x2){return;}itemHorizontalAlignment=_0x59127e;}function setItemVerticalAlignment(_0x336b50){if(_0x336b50!=0x0&&_0x336b50!=0x1&&_0x336b50!=0x2){return;}itemVerticalAlignment=_0x336b50;}function drawTextInWidth(_0x3934fe,_0x496672,_0x48efc2,_0x55bc3c,_0x40071e){if(_0x3934fe==null||_0x3934fe[_0xbd5c('0x3')]<=0x0){return![];}var _0x4c66c4=covert(_0x40071e)+_0xbd5c('0x7');var _0x117f20=covert(_0x496672);var _0x269c27=covert(_0x48efc2);var _0x5c42d2=covert(_0x55bc3c);context[_0xbd5c('0x8')]=_0x4c66c4;const _0x374d21=context[_0xbd5c('0xd')](_0x3934fe)['width'];switch(itemHorizontalAlignment){case 0x0:{break;}case 0x1:{_0x117f20+=(_0x5c42d2-_0x374d21)*0.5;break;}case 0x2:{_0x117f20+=_0x5c42d2-_0x374d21;break;}default:break;}context['fillText'](_0x3934fe,_0x117f20,_0x269c27);return!![];}function drawText(_0x2c318b,_0x48673d,_0x1eaa0f,_0x5aaebc){setItemHorizontalAlignment(0x0);drawTextInWidth(_0x2c318b,_0x48673d,_0x1eaa0f,printWidth,_0x5aaebc);}function drawBarcode(_0x2bb9ab,_0x2e3dd1,_0x9820f,_0x16e65e,_0x1a6ad8){_0x2e3dd1=covert(_0x2e3dd1);_0x9820f=covert(_0x9820f);_0x16e65e=covert(_0x16e65e);_0x1a6ad8=covert(_0x1a6ad8);context[_0xbd5c('0x11')]();switch(itemOrientation){case 0x1:case 0x5a:{context['rotate'](Math['PI']/0x2);context[_0xbd5c('0x18')](parseInt(_0x9820f-_0x2e3dd1),parseInt(-_0x2e3dd1-_0x9820f-_0x16e65e));var _0xb73314=_0x16e65e;_0x16e65e=_0x1a6ad8;_0x1a6ad8=_0xb73314;break;}case 0x2:case 0xb4:{context[_0xbd5c('0x15')](Math['PI']);context['translate'](parseInt(-_0x2e3dd1*0x2-_0x16e65e),parseInt(-_0x9820f*0x2-_0x1a6ad8));break;}case 0x3:case 0x10e:{context['rotate'](-Math['PI']/0x2);context['translate'](parseInt(-_0x2e3dd1-_0x9820f-_0x1a6ad8),parseInt(_0x2e3dd1-_0x9820f));var _0xb73314=_0x16e65e;_0x16e65e=_0x1a6ad8;_0x1a6ad8=_0xb73314;break;}default:break;}barcodeWriter['barcode'](context,_0x2bb9ab,_0x2e3dd1,_0x9820f,_0x16e65e,_0x1a6ad8);context['restore']();}function drawQRCode(_0x37d0da,_0x59a2a4,_0x484e00,_0x3f8f17,_0x207f5f,_0xc7cc48){_0x59a2a4=covert(_0x59a2a4);_0x484e00=covert(_0x484e00);_0x3f8f17=covert(_0x3f8f17);_0x207f5f=_0x207f5f==null?_0x3f8f17:_0x207f5f;_0x207f5f=covert(_0x207f5f);context['save']();switch(itemOrientation){case 0x1:case 0x5a:{context[_0xbd5c('0x15')](Math['PI']/0x2);context[_0xbd5c('0x18')](parseInt(_0x484e00-_0x59a2a4),parseInt(-_0x59a2a4-_0x484e00-_0x3f8f17));var _0x5a221f=_0x3f8f17;_0x3f8f17=_0x207f5f;_0x207f5f=_0x5a221f;break;}case 0x2:case 0xb4:{context[_0xbd5c('0x15')](Math['PI']);context[_0xbd5c('0x18')](parseInt(-_0x59a2a4*0x2-_0x3f8f17),parseInt(-_0x484e00*0x2-_0x207f5f));break;}case 0x3:case 0x10e:{context['rotate'](-Math['PI']/0x2);context['translate'](parseInt(-_0x59a2a4-_0x484e00-_0x207f5f),parseInt(_0x59a2a4-_0x484e00));var _0x5a221f=_0x3f8f17;_0x3f8f17=_0x207f5f;_0x207f5f=_0x5a221f;break;}default:break;}barcodeWriter['qrcode'](context,_0x37d0da,_0x59a2a4,_0x484e00,_0x3f8f17,_0x207f5f,_0xc7cc48);context['restore']();}function drawLine(_0x39850a,_0x50103b,_0xc316cc,_0x26c3fd){drawRectangle(_0x39850a,_0x50103b,_0xc316cc,_0x26c3fd,0x0,!![]);}function drawRectangle(_0x2dd9b2,_0x35a3cc,_0x417f73,_0x48788c,_0x2bc18f,_0xc6ed95){_0x2dd9b2=covert(_0x2dd9b2);_0x35a3cc=covert(_0x35a3cc);_0x417f73=covert(_0x417f73);_0x48788c=covert(_0x48788c);_0x2bc18f=covert(_0x2bc18f);context[_0xbd5c('0x11')]();switch(itemOrientation){case 0x1:case 0x5a:{context[_0xbd5c('0x15')](Math['PI']/0x2);context[_0xbd5c('0x18')](parseInt(_0x35a3cc-_0x2dd9b2),parseInt(-_0x2dd9b2-_0x35a3cc-_0x417f73));var _0x442086=_0x417f73;_0x417f73=_0x48788c;_0x48788c=_0x442086;break;}case 0x2:case 0xb4:{context['rotate'](Math['PI']);context['translate'](parseInt(-_0x2dd9b2*0x2-_0x417f73),parseInt(-_0x35a3cc*0x2-_0x48788c));break;}case 0x3:case 0x10e:{context['rotate'](-Math['PI']/0x2);context['translate'](parseInt(-_0x2dd9b2-_0x35a3cc-_0x48788c),parseInt(_0x2dd9b2-_0x35a3cc));var _0x442086=_0x417f73;_0x417f73=_0x48788c;_0x48788c=_0x442086;break;}default:break;}context[_0xbd5c('0x9')]=_0x2bc18f;if(_0xc6ed95){context[_0xbd5c('0x1b')]='#000000';context[_0xbd5c('0x14')](_0x2dd9b2,_0x35a3cc,_0x417f73,_0x48788c);}else{context[_0xbd5c('0x2')](_0x2dd9b2,_0x35a3cc,_0x417f73,_0x48788c);}context[_0xbd5c('0x10')]();}function drawImagePath(_0x50826a,_0x11d11d,_0xe0ca6a,_0x41eb03,_0x434844,_0x5ed521){_0x11d11d=covert(_0x11d11d);_0xe0ca6a=covert(_0xe0ca6a);_0x41eb03=covert(_0x41eb03);_0x434844=covert(_0x434844);context[_0xbd5c('0x11')]();switch(itemOrientation){case 0x1:case 0x5a:{context['rotate'](Math['PI']/0x2);context[_0xbd5c('0x18')](parseInt(_0xe0ca6a-_0x11d11d),parseInt(-_0x11d11d-_0xe0ca6a-_0x41eb03));var _0x1aa673=_0x41eb03;_0x41eb03=_0x434844;_0x434844=_0x1aa673;break;}case 0x2:case 0xb4:{context[_0xbd5c('0x15')](Math['PI']);context['translate'](parseInt(-_0x11d11d*0x2-_0x41eb03),parseInt(-_0xe0ca6a*0x2-_0x434844));break;}case 0x3:case 0x10e:{context['rotate'](-Math['PI']/0x2);context[_0xbd5c('0x18')](parseInt(-_0x11d11d-_0xe0ca6a-_0x434844),parseInt(_0x11d11d-_0xe0ca6a));var _0x1aa673=_0x41eb03;_0x41eb03=_0x434844;_0x434844=_0x1aa673;break;}default:break;}const _0x1b89ea=wx['createOffscreenCanvas']({'type':'2d','width':_0x41eb03,'height':_0x434844});const _0x162883=_0x1b89ea[_0xbd5c('0xe')]();_0x162883['src']=_0x50826a;_0x162883['onload']=()=>{context['drawImage'](_0x162883,_0x11d11d,_0xe0ca6a,_0x41eb03,_0x434844);context[_0xbd5c('0x10')]();if(_0x5ed521){_0x5ed521();}};}module['exports']={'setPrintPageGapType':setPrintPageGapType,'setPrintPageGapLength':setPrintPageGapLength,'setPrintDarkness':setPrintDarkness,'setPrintSpeed':setPrintSpeed,'setPrintGrayThreshold':setPrintGrayThreshold,'setSupportPrefixs':setSupportPrefixs,'connectingPrinterName':connectingPrinterName,'scanedPrinters':scanedPrinters,'openPrinter':openPrinter,'connectingPrinterDetailInfos':connectingPrinterDetailInfos,'closePrinter':closePrinter,'startDrawLabel':startDrawLabel,'endDrawLabel':endDrawLabel,'print':print,'setItemOrientation':setItemOrientation,'setItemHorizontalAlignment':setItemHorizontalAlignment,'setItemVerticalAlignment':setItemVerticalAlignment,'drawText':drawText,'drawTextInWidth':drawTextInWidth,'drawBarcode':drawBarcode,'drawQRCode':drawQRCode,'drawLine':drawLine,'drawRectangle':drawRectangle,'drawImagePath':drawImagePath};