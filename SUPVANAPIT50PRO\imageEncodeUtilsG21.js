import printingControl from"\u002E\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import supVanPrintUtilsG21 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";export default{"matWidth":25,'\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068':2,'\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074':30,'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':8,"imageRgbaData":[],'\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074':[],"OverturnType":0,'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074':0,'\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065':false,'\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074':[],"objectData":null,"printType":'','\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,async initEncodeData(objectData,imageRgbaData){try{console['\u006C\u006F\u0067']("12GslitUedocnEegami".split("").reverse().join(""),objectData);var _0x1f667f;const that=this;_0x1f667f=(981814^981814)+(902061^902058);that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?210570^210571:334947^334947;that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},async initializeData(){const that=this;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();let length=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068'];that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-(777189^777185);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(508464^508467)){that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']=length-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*(797186^797184);}if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(656307^656307)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=157993^157992;}var _0xe3a;let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(222527^222525))*(717678^717620);_0xe3a=871192^871192;let object={'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],"Width":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"Height":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065':rotateAngle,'\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(595361^595365),'\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(938676^938672)+1.5*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"OverturnType":that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],"DeviceSn":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{var _0xc6be=(751389^751387)+(323872^323879);const that=this;_0xc6be=(492707^492715)+(543971^543968);var _0xd466ga=(589052^589054)+(765543^765542);let bufferTransferCount=870695^870695;_0xd466ga=389007^389002;let num=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u006F\u0070\u0069\u0065\u0073'];let _bufLength=251279^247183;var _0x598bbf=(974635^974633)+(665919^665913);let countBuff=new Array();_0x598bbf=(571121^571121)+(383555^383556);let isEndFlag=!![];var _0xa84cb=(289785^289786)+(923098^923103);let imgTotalCount=307777^307776;_0xa84cb="ckcqfp".split("").reverse().join("");let marginleft=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']();let marginright=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']();console['\u006C\u006F\u0067']("\u56FE\u7247\u5DE6\u8FB9\u8DDD",marginleft);var _0x8c_0xeea=(275390^275386)+(778669^778666);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft;_0x8c_0xeea='\u0062\u0069\u006B\u006B\u006D\u0068';console['\u006C\u006F\u0067']("\u56FE\u7247\u5217\u6570",_nColumnTotalCnt);let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(351524^351523))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u6570\u8282\u5B57\u5217\u6BCF".split("").reverse().join(""),nBytePerLine);let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(478060^478074))/nBytePerLine);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u7F13\u51B2\u533A\u6700\u5927\u5217\u6570",nMax);var _0xf89bbb;let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(940190^940191))/nMax);_0xf89bbb=(789740^789732)+(752418^752416);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u56FE\u7247\u7F13\u51B2\u533A\u6570\u91CF\u0020\u0062\u0075\u0066\u0066\u0065\u0072\u0043\u006F\u0075\u006E\u0074\u0049\u006D\u0061\u0067\u0065",bufferCountImage);var _0xfbea=(202665^202665)+(555177^555181);let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();_0xfbea=635165^635162;console['\u006C\u006F\u0067']("\u5EA6\u957Fsetyblla".split("").reverse().join(""),allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=263055^263055;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=890868^890868;i<bufferCountImage;i++){var _0xcf0d;let _btBuf=new Uint8Array(_bufLength);_0xcf0d=298901^298901;pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u0075\u0074\u0054\u0079\u0070\u0065']);pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](540537^540536);pr['\u0073\u0065\u0074\u0053\u0061\u0076\u0065\u0070\u0061\u0070\u0065\u0072'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0053\u0061\u0076\u0065\u0050\u0061\u0070\u0065\u0072']?947046^947047:726998^726998);pr['\u0073\u0065\u0074\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==null&&that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']!==undefined?that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']:583789^583789);if(i==(590556^590556)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](712908^712909);}let bufferColumnCnt=525160^525160;if(i==bufferCountImage-(869095^869094)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](922278^922279);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](753642^753643);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;let end=star+bufferColumnCnt*nBytePerLine;let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_btBuf[831630^831626]=bufferColumnCnt&(661129^661110);_btBuf[697959^697954]=bufferColumnCnt>>(298173^298165)&(476968^477143);for(var y=106463^106463;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(530560^530574)+y]=b[y];}var _0xfc409a=(510462^510461)+(561632^561641);let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](867750^867744);_0xfc409a=(677670^677664)+(707767^707765);for(var z=255358^255356;z<(291212^291208);z++){_btBuf[z]=btdata[z-(233506^233504)];}_btBuf[546115^546117]=nBytePerLine&(277085^277154);var _0x214d;let left=592823^592823;_0x214d=(179899^179898)+(467174^467175);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']!=(673926^673926)){left=834093^834092;}if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(633292^633295)){var _0x3c23cb;let marginLeft=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']+left)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x3c23cb=476939^476937;let marginRight=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u004D\u0061\u0072\u0067\u0069\u006E']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_btBuf[301718^301726]=marginLeft&(423706^423909);_btBuf[217139^217146]=marginLeft>>(407255^407263)&(459817^459990);_btBuf[815907^815913]=marginRight&(247503^247344);_btBuf[618706^618713]=marginRight>>(653644^653636)&(315633^315406);}else{var _0x3bdf=(522939^522940)+(886263^886259);let marginLeft=((that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(132284^132280))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(252060^252062)+left*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x3bdf=(603844^603843)+(466543^466540);var _0x14a6e;let marginRight=(that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']+(724789^724785))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006D\u0061\u0074\u004C\u0065\u006E\u0067\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginLeft;_0x14a6e=(548488^548490)+(863558^863559);let margin=Math['\u006D\u0061\u0078'](237874^237878,marginLeft);_btBuf[313392^313400]=margin&(347618^347421);_btBuf[798311^798318]=margin>>(917691^917683)&(242184^242423);margin=Math['\u006D\u0061\u0078'](362056^362060,marginRight);_btBuf[863000^862994]=margin&(855695^855664);_btBuf[360861^360854]=margin>>(906971^906963)&(280984^280935);}_btBuf[593871^593859]=554575^554575;_btBuf[970346^970343]=254422^254422;var _0xae9beb=(510287^510286)+(325716^325716);let len=_btBuf[867565^867560];_0xae9beb=(175142^175150)+(787954^787955);len<<=291553^291561;len+=_btBuf[369479^369475];len*=_btBuf[262576^262582];len+=858694^858696;var _0xg3eaf;let un=135276^135276;_0xg3eaf='\u006A\u006A\u0062\u0065\u006B\u0066';for(var j=431232^431234;j<(378150^378152);j++){un+=_btBuf[j];}let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(750231^750487));if(x>(775147^775147)){for(var k=225312^225312;k<x;k++){un+=_btBuf[(k+(556170^556171))*(552682^552938)-(374879^374878)];}}_btBuf[199645^199645]=un;_btBuf[856710^856711]=un>>(935835^935827);countBuff['\u0070\u0075\u0073\u0068'](_btBuf);console['\u006C\u006F\u0067']("\u0070\u0072",pr);}}while(bufferTransferCount<bufferCountImage*num&&countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']>(591201^591201)){let sendData=null;var _0xd2gbbb=(742429^742431)+(914165^914162);let bufferCount=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);_0xd2gbbb=413770^413775;var _0x62ee;let bufferCountOne=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);_0x62ee='\u006A\u006F\u006C\u006B\u0065\u006F';do{let bufferPackage=Array();for(let a=397474^397474;a<bufferCount;a++){for(var b=705868^705868;b<_bufLength;b++){bufferPackage[b+a*_bufLength]=countBuff[a][b];}}try{this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=[];this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=bufferPackage;sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](bufferPackage);}catch(error){console['\u006C\u006F\u0067']("\u538B\u7F29\u5931\u8D25");sendData=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];num=200222^200222;throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}bufferCount--;}while(sendData['\u004C\u0065\u006E\u0067\u0074\u0068']>_bufLength);bufferTransferCount=bufferTransferCount+bufferCount+(442477^442476);countBuff['\u0073\u0070\u006C\u0069\u0063\u0065'](380848^380848,bufferCountOne);if(sendData['\u006C\u0065\u006E\u0067\u0074\u0068']>(600384^600384)){that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}}}catch(error){throw error;}},async doPrint(){const that=this;console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u957F\u5EA6",that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']);console['\u006C\u006F\u0067']("\u5EA6\u957F)(muNllAegamIteg.eganaMhtooTELB".split("").reverse().join(""),bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']());if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(583761^583761)){supVanPrintUtilsG21['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u6253\u5370\u5B57\u6A21\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"));}}else{supVanPrintUtilsG21['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=482471^482471;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};