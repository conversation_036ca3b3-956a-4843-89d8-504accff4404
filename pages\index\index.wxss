/**index.wxss**/
page {
  background-color: #F4F9FD;
}
.connect-blue {
  display: flex;
  justify-content: center;
  background-color: antiquewhite;
  margin: 20rpx 0;

}

.connect-blue text {
  font-size: 31rpx;
  font-weight: 500;
  color: #000000;
  padding: 20rpx 0rpx;
}

.list-wrap {
  width: 750rpx;
  background: #F7F8FA;
  margin-bottom: 23rpx;
}

.shop-name {
  display: flex;
  margin: 10rpx;
  padding: 30rpx;
  background-color: antiquewhite;
}

.text-shop-name {
  font-size: 23rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #999999;
}

.text-shop-name-content {
  font-size: 25rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
  margin-top: 10rpx;
}

.printnum-text {
  display: flex;
  justify-content: center;
  background-color: antiquewhite;
  margin: 20rpx 0;

}
.input-text {
  display: flex;

  align-items: center;
  height: 50rpx;
}

.preview-image-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.preview-image {
  width: 80%;
  max-height: 80%;
  background-color: white;
  border-radius: 8px;
}

.close-preview {
  margin-top: 20px;
  padding: 8px 16px;
  background-color: #007AFF;
  color: white;
  border-radius: 4px;
}

/* 编辑模版弹窗样式 */
.edit-template-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.edit-template-content {
  width: 90%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  max-height: 80%;
  overflow-y: auto;
}

.edit-template-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}

.edit-template-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.close-edit-template {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: #999;
  background-color: #f5f5f5;
  border-radius: 50%;
}

.edit-preview-container {
  margin-bottom: 30rpx;
  text-align: center;
}

.edit-preview-image {
  width: 100%;
  max-width: 400rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
}

.edit-preview-placeholder {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.edit-form {
  margin-bottom: 40rpx;
}

.edit-form-item {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.edit-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #333;
  flex-shrink: 0;
}

.edit-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.edit-actions {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.edit-btn {
  flex: 1;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.edit-btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.edit-btn-print {
  background-color: #007AFF;
  color: white;
}

