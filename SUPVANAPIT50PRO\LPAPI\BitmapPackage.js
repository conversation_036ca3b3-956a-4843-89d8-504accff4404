const _0xb7b0=['exports','gapLength','topMargin','supportSuperBitmap','toString','printerDPI','length','log','softwareFlags','ceil','grayThreshold','printerWidth','value','floor','printSpeed','min','height'];const _0x1f17=function(_0xb7b04c,_0x1f170d){_0xb7b04c=_0xb7b04c-0x0;let _0x2c175b=_0xb7b0[_0xb7b04c];return _0x2c175b;};const HOST_TO_DEVICE_DATA_START=0x1f;const DEVICE_TO_HOST_DATA_START=0x1f;const FIXED_PACKAGE_CRC_RESULT=0x88;const DefaultThreshold=0x80;const MaxEBVValue=0x3fff;const SuperBitmapRLE5Length=[0x1,0x2,0x3,0x4,0x5,0x6,0x7,0x8,0x9,0xa,0xb,0xc,0x18,0x24,0x30,0x78];const SuperBitmapRLE6Length=[0x1,0x2,0x3,0x4,0x5,0x6,0x7,0x8,0x9,0xa,0xb,0xc,0xd,0xe,0xf,0x10,0x11,0x12,0x13,0x14,0x29,0x3e,0x53,0x68,0x7d,0x92,0xa7,0xbc,0xd1,0xe6,0x1cd,0x39b];const PCPDSF_RLE5_BITMAP=0x10;const PCPDSF_RLE6_BITMAP=0x20;const PCPDSF_RLEC_BITMAP=0x80;const LineActionNone=0x0;const LineActionLine=0x1;const LineActionPrint=0x2;let grayThreshold;let supportSuperBitmap;let softwareFlags;let mLineAction=LineActionNone;let mPrinterDPI;let mPrinterWidth;let mByteWidth;let mLineCount;let mLineBytes;let mLineData;let mPrevBytes;let mPrevData;let mSumLines;let mSumPrints;let mSumRLE5Xs;let mSumRLE5Ds;let mSumRLE6Xs;let mSumRLE6Ds;let mSumRLE_Cs;let mSumRepeats;let mRLE5XSaved;let mRLE5DSaved;let mRLE6XSaved;let mRLE6DSaved;let mRLECSaved;let mArrayBitmap;function pageStartData(){return[HOST_TO_DEVICE_DATA_START,0x20,0x0,FIXED_PACKAGE_CRC_RESULT];}function pageEndData(){return[HOST_TO_DEVICE_DATA_START,0x28,0x0,FIXED_PACKAGE_CRC_RESULT];}function pageWidthData(_0x1af1bf){_0x1af1bf=Math[_0x1f17('0xd')]((_0x1af1bf+0x7)/0x8);if(_0x1af1bf>=0xc0){return[HOST_TO_DEVICE_DATA_START,0x27,0x2,0xc0|_0x1af1bf>>>0x8,_0x1af1bf&0xff,FIXED_PACKAGE_CRC_RESULT];}else{return[HOST_TO_DEVICE_DATA_START,0x27,0x1,_0x1af1bf,FIXED_PACKAGE_CRC_RESULT];}}function pageHeightData(_0x845af3){if(_0x845af3>MaxEBVValue){return[HOST_TO_DEVICE_DATA_START,0x26,0x3,0xc0,_0x845af3>>>0x8,_0x845af3&0xff,FIXED_PACKAGE_CRC_RESULT];}else if(_0x845af3>=0xc0){return[HOST_TO_DEVICE_DATA_START,0x26,0x2,0xc0|_0x845af3>>>0x8,_0x845af3&0xff,FIXED_PACKAGE_CRC_RESULT];}else{return[HOST_TO_DEVICE_DATA_START,0x26,0x1,_0x845af3,FIXED_PACKAGE_CRC_RESULT];}}function settingGapTypeData(_0x4c26ff){return[HOST_TO_DEVICE_DATA_START,0x42,0x1,_0x4c26ff,FIXED_PACKAGE_CRC_RESULT];}function settingGapLengthData(_0x45a58a){if(_0x45a58a>MaxEBVValue){return[HOST_TO_DEVICE_DATA_START,0x45,0x3,0xc0,_0x45a58a>>>0x8,_0x45a58a&0xff,FIXED_PACKAGE_CRC_RESULT];}else if(_0x45a58a>=0xc0){return[HOST_TO_DEVICE_DATA_START,0x45,0x2,0xc0|_0x45a58a>>>0x8,_0x45a58a&0xff,FIXED_PACKAGE_CRC_RESULT];}else{return[HOST_TO_DEVICE_DATA_START,0x45,0x1,_0x45a58a,FIXED_PACKAGE_CRC_RESULT];}}function settingStrengthData(_0x384f53){return[HOST_TO_DEVICE_DATA_START,0x43,0x1,_0x384f53,FIXED_PACKAGE_CRC_RESULT];}function settingSpeedData(_0x43c1c5){return[HOST_TO_DEVICE_DATA_START,0x44,0x1,_0x43c1c5,FIXED_PACKAGE_CRC_RESULT];}function arrayWithParam(_0x17edf7){start(_0x17edf7);print(_0x17edf7);return end(_0x17edf7);}function arrayWithImage(_0x3b1161,_0x5f4906,_0xa34871,_0x35d615,_0x5af35e,_0x336d11,_0x2ef3c1,_0x2cb2fd,_0x3a216f,_0x177d26,_0x40cfe5,_0x2a1233){return arrayWithParam({'pixelDatas':_0x3b1161,'printerDPI':_0x5f4906,'printerWidth':_0xa34871,'width':_0x35d615,'height':_0x5af35e,'gapType':_0x336d11,'gapLength':_0x2ef3c1,'printDarkness':_0x2cb2fd,'printSpeed':_0x3a216f,'grayThreshold':_0x177d26,'supportSuperBitmap':_0x40cfe5,'softwareFlags':_0x2a1233});}function start(_0x804c57){mArrayBitmap=[];grayThreshold=_0x804c57['grayThreshold']==null?DefaultThreshold:_0x804c57[_0x1f17('0xa')];supportSuperBitmap=_0x804c57[_0x1f17('0x3')]==null?!![]:_0x804c57[_0x1f17('0x3')];softwareFlags=_0x804c57[_0x1f17('0x8')]==null?PCPDSF_RLE6_BITMAP:_0x804c57[_0x1f17('0x8')];console[_0x1f17('0x7')](grayThreshold);console[_0x1f17('0x7')](supportSuperBitmap);console[_0x1f17('0x7')](softwareFlags);mLineAction=LineActionLine;mPrinterDPI=_0x804c57['printerDPI']==null?0xcb:_0x804c57[_0x1f17('0x5')];mPrinterWidth=_0x804c57[_0x1f17('0xb')]==null?0x180:_0x804c57['printerWidth'];mByteWidth=Math[_0x1f17('0xd')]((mPrinterWidth+0x7)/0x8);mLineCount=_0x804c57[_0x1f17('0x2')]==null?0x0:_0x804c57[_0x1f17('0x2')];mLineBytes=0x0;mLineData=null;mPrevBytes=0x0;mPrevData=null;mSumLines=0x0;mSumPrints=0x0;mSumRLE5Xs=0x0;mSumRLE5Ds=0x0;mSumRLE6Xs=0x0;mSumRLE6Ds=0x0;mSumRLE_Cs=0x0;mSumRepeats=0x0;mRLE5XSaved=0x0;mRLE5DSaved=0x0;mRLE6XSaved=0x0;mRLE6DSaved=0x0;mRLECSaved=0x0;pushPackage(pageStartData());pushPackage(pageWidthData(_0x804c57['width']));pushPackage(pageHeightData(_0x804c57[_0x1f17('0x10')]));if(_0x804c57['gapType']>=0x0){pushPackage(settingGapTypeData(_0x804c57['gapType']));}if(_0x804c57[_0x1f17('0x1')]>0x0){pushPackage(settingGapLengthData(_0x804c57['gapLength']));}if(_0x804c57['printDarkness']>0x0){pushPackage(settingStrengthData(_0x804c57['printDarkness']-0x1));}if(_0x804c57['printSpeed']>0x0){pushPackage(settingSpeedData(_0x804c57[_0x1f17('0xe')]-0x1));}}function print(_0x4e414f){let _0x46233b=_0x4e414f['pixelDatas'];let _0x1766b6=_0x4e414f['width'];let _0x3f7f9c=_0x4e414f[_0x1f17('0x10')];let _0xb5dddd=Math[_0x1f17('0xf')](_0x1766b6,mPrinterWidth);let _0x315f10=Math[_0x1f17('0xd')]((_0xb5dddd+0x7)/0x8);for(let _0x17c24b=0x0;_0x17c24b<_0x3f7f9c;++_0x17c24b){let _0x36fac9=allocIntArray(_0x315f10);let _0x2c2e5c=_0x1766b6*_0x17c24b*0x4;let _0x3a8a44=0x80;let _0x26b6da=0x0;for(let _0x4cb925=0x0;_0x4cb925<_0xb5dddd;++_0x4cb925){let _0x3ed757=_0x46233b[_0x2c2e5c+0x0];let _0x4a8ac5=_0x46233b[_0x2c2e5c+0x1];let _0x1ed880=_0x46233b[_0x2c2e5c+0x2];let _0x2ced0a=_0x46233b[_0x2c2e5c+0x3];if(_0x2ced0a>0x0){let _0x356e58=_0x3ed757*0.3+_0x4a8ac5*0.59+_0x1ed880*0.11;if(_0x356e58<=grayThreshold){_0x36fac9[_0x26b6da]=_0x36fac9[_0x26b6da]|_0x3a8a44;}}if(_0x3a8a44===0x1){_0x3a8a44=0x80;++_0x26b6da;}else{_0x3a8a44=_0x3a8a44>>>0x1;}_0x2c2e5c+=0x4;}printRow(_0x36fac9);}}function end(_0x70b802){let _0x144383=_0x70b802['bottomMargin']==null?0x0:_0x70b802['bottomMargin'];switch(mLineAction){case LineActionLine:pushLine(mLineCount+_0x144383);break;case LineActionPrint:pushPrint();pushLine(_0x144383);break;default:return null;}mLineAction=LineActionNone;pushPackage(pageEndData());return mArrayBitmap;}function printRow(_0x22cb8e){let _0x20f854=_0x22cb8e['length']-0x1;for(;_0x20f854>=0x0;--_0x20f854){if(_0x22cb8e[_0x20f854]!==0x0)break;}if(_0x20f854<0x0){return printLine(0x1);}++_0x20f854;switch(mLineAction){case LineActionLine:pushLine(mLineCount);break;case LineActionPrint:if(mLineBytes===_0x20f854&&checkArrayEquals(mLineData,_0x22cb8e,_0x20f854)){mLineCount+=0x1;return!![];}pushPrint();break;default:return![];}mLineData=_0x22cb8e;mLineBytes=_0x20f854;mLineCount=0x1;mLineAction=LineActionPrint;return!![];}function printLine(_0x8a7b26){switch(mLineAction){case LineActionLine:mLineCount+=_0x8a7b26;return!![];case LineActionPrint:pushPrint();break;default:return![];}mLineData=null;mLineBytes=0x0;mLineCount=_0x8a7b26;mLineAction=LineActionLine;return!![];}function pushLine(_0x3863ce){if(_0x3863ce<=0x0)return;mSumLines+=_0x3863ce;mPrevData=null;mPrevBytes=0x0;let _0x4c9e14=[0x1b,0x4a,0xff];for(;_0x3863ce>=0xff;_0x3863ce-=0xff){pushPackage(_0x4c9e14);}if(_0x3863ce>0x0){pushPackage([0x1b,0x4a,_0x3863ce]);}}function pushPrint(){if(mLineCount<=0x0)return;let _0x247cf1=0x0;for(;_0x247cf1<mLineBytes;++_0x247cf1){if(mLineData[_0x247cf1]!==0x0)break;}let _0x25a950=mLineBytes-_0x247cf1;let _0x17c5aa=null,_0x1d5339=null,_0x312171=null,_0x582c14=null,_0x38b9b4=null;let _0x5a1b3f=0x0,_0x4c8b6a=0x0,_0x62685b=0x0,_0x5846b8=0x0,_0x50fc41=0x0;if(supportSuperBitmap){if((softwareFlags&PCPDSF_RLEC_BITMAP)!==0x0){_0x17c5aa=allocIntArray(mByteWidth+0x4);_0x5a1b3f=calcRLEC(mLineData,mLineBytes,_0x17c5aa);}if((softwareFlags&PCPDSF_RLE5_BITMAP)!==0x0){_0x1d5339=allocIntArray(mByteWidth+0x4);_0x4c8b6a=calcRLE5X(mLineData,mLineBytes,_0x1d5339);}if((softwareFlags&PCPDSF_RLE5_BITMAP)!==0x0&&mPrevData!==null){_0x312171=allocIntArray(mByteWidth+0x4);_0x62685b=calcRLE5D(mPrevData,mPrevBytes,mLineData,mLineBytes,_0x312171);}if((softwareFlags&PCPDSF_RLE6_BITMAP)!==0x0){_0x582c14=allocIntArray(mByteWidth+0x4);_0x5846b8=calcRLE6X(mLineData,mLineBytes,_0x582c14);}if((softwareFlags&PCPDSF_RLE6_BITMAP)!==0x0&&mPrevData!==null){_0x38b9b4=allocIntArray(mByteWidth+0x4);_0x50fc41=calcRLE6D(mPrevData,mPrevBytes,mLineData,mLineBytes,_0x38b9b4);}}let _0x1abd65=(_0x247cf1>=0xc0?0x4:0x3)+(_0x25a950>=0xc0?0x2:0x1)+_0x25a950;let _0x5b3971=_0x5a1b3f<=0x0?mByteWidth+0x64:_0x5a1b3f+(_0x5a1b3f>=0xc0?0x4:0x3);let _0x3e0bb3=_0x4c8b6a<=0x0?mByteWidth+0x64:Math['ceil'](_0x4c8b6a*0x5/0x8)+(_0x4c8b6a>=0xc0?0x4:0x3);let _0x501311=_0x62685b<=0x0?mByteWidth+0x64:Math[_0x1f17('0x9')](_0x62685b*0x5/0x8)+(_0x62685b>=0xc0?0x4:0x3);let _0x1807b0=_0x5846b8<=0x0?mByteWidth+0x64:Math['ceil'](_0x5846b8*0x6/0x8)+(_0x5846b8>=0xc0?0x4:0x3);let _0x42162e=_0x50fc41<=0x0?mByteWidth+0x64:Math['ceil'](_0x50fc41*0x6/0x8)+(_0x50fc41>=0xc0?0x4:0x3);if(_0x501311<_0x1abd65&&_0x501311<_0x5b3971&&_0x501311<_0x3e0bb3&&_0x501311<_0x1807b0&&_0x501311<=_0x42162e){mSumRLE5Ds+=0x1;mRLE5DSaved+=_0x1abd65-_0x501311;pushRLE5(0x2d,_0x312171,_0x62685b);}else if(_0x42162e<_0x1abd65&&_0x42162e<_0x5b3971&&_0x42162e<_0x3e0bb3&&_0x42162e<_0x1807b0){mSumRLE6Ds+=0x1;mRLE6DSaved+=_0x1abd65-_0x42162e;pushRLE6(0x3d,_0x38b9b4,_0x50fc41);}else if(_0x3e0bb3<_0x1abd65&&_0x3e0bb3<_0x5b3971&&_0x3e0bb3<=_0x1807b0){mSumRLE5Xs+=0x1;mRLE5XSaved+=_0x1abd65-_0x3e0bb3;pushRLE5(0x2c,_0x1d5339,_0x4c8b6a);}else if(_0x1807b0<_0x1abd65&&_0x1807b0<_0x5b3971){mSumRLE6Xs+=0x1;mRLE6XSaved+=_0x1abd65-_0x1807b0;pushRLE6(0x3c,_0x582c14,_0x5846b8);}else if(_0x5b3971<_0x1abd65){mSumRLE_Cs+=0x1;mRLECSaved+=_0x1abd65-_0x5b3971;pushRLEC(0x29,_0x17c5aa,_0x5a1b3f);}else{mSumPrints+=0x1;let _0x2d142a=[HOST_TO_DEVICE_DATA_START,0x2b,0x0,0x0];let _0x15c924=pushEBV(_0x2d142a,0x2,_0x247cf1);_0x15c924=pushEBV(_0x2d142a,_0x15c924,_0x25a950);pushPackage2(_0x2d142a,0x0,_0x15c924,mLineData,_0x247cf1,mLineBytes);}if(mLineCount>0x1){pushRepeat(mLineCount-0x1);}mPrevData=mLineData;mPrevBytes=mLineBytes;}function pushRepeat(_0x1c4b48){if(_0x1c4b48<=0x0)return;mSumRepeats+=_0x1c4b48;let _0x4bfef5=MaxEBVValue;let _0x423cb3=[HOST_TO_DEVICE_DATA_START,0x2e,0x0];pushEBV(_0x423cb3,0x2,_0x4bfef5);for(;_0x1c4b48>_0x4bfef5;_0x1c4b48-=_0x4bfef5+0x1){pushPackage(_0x423cb3);}if(_0x1c4b48>0x0){_0x423cb3=[HOST_TO_DEVICE_DATA_START,0x2e,0x0];pushEBV(_0x423cb3,0x2,_0x1c4b48-0x1);pushPackage(_0x423cb3);}}function pushRLEC(_0x424601,_0x37fb42,_0x2ec3d3){if(_0x2ec3d3<=0x0)return;let _0x581c5f=[HOST_TO_DEVICE_DATA_START,_0x424601,0x0];let _0x3ba0fb=pushEBV(_0x581c5f,0x2,_0x2ec3d3);pushPackage2(_0x581c5f,0x0,_0x3ba0fb,_0x37fb42,0x0,_0x2ec3d3);}function appendRLEC(_0x54ea14,_0x5af137,_0x5ad05d,_0x496c86){for(;_0x496c86>=0xff-0xc0;_0x496c86-=0xff-0xc0){if(_0x5af137['value']+0x2>mByteWidth)return![];_0x54ea14[_0x5af137[_0x1f17('0xc')]]=0xff;++_0x5af137[_0x1f17('0xc')];_0x54ea14[_0x5af137['value']]=_0x5ad05d;++_0x5af137['value'];}switch(_0x496c86){case 0x1:if(_0x5ad05d>0xc0){if(_0x5af137['value']+0x2>mByteWidth)return![];_0x54ea14[_0x5af137['value']]=0xc1;++_0x5af137['value'];_0x54ea14[_0x5af137[_0x1f17('0xc')]]=_0x5ad05d;++_0x5af137[_0x1f17('0xc')];}else{if(_0x5af137[_0x1f17('0xc')]+0x1>mByteWidth)return![];_0x54ea14[_0x5af137['value']]=_0x5ad05d;++_0x5af137['value'];}break;case 0x2:if(_0x5af137[_0x1f17('0xc')]+0x2>mByteWidth)return![];if(_0x5ad05d>0xc0){_0x54ea14[_0x5af137[_0x1f17('0xc')]]=0xc2;++_0x5af137['value'];_0x54ea14[_0x5af137['value']]=_0x5ad05d;++_0x5af137['value'];}else{_0x54ea14[_0x5af137['value']]=_0x5ad05d;++_0x5af137['value'];_0x54ea14[_0x5af137['value']]=_0x5ad05d;++_0x5af137[_0x1f17('0xc')];}break;default:if(_0x496c86>0x0){if(_0x5af137[_0x1f17('0xc')]+0x2>mByteWidth)return![];_0x54ea14[_0x5af137[_0x1f17('0xc')]]=_0x496c86|0xc0;++_0x5af137[_0x1f17('0xc')];_0x54ea14[_0x5af137[_0x1f17('0xc')]]=_0x5ad05d;++_0x5af137['value'];}break;}return!![];}function calcRLEC(_0x1d4e5b,_0xbee4b1,_0x52b3fb){if(_0xbee4b1<=0x0)return 0x0;let _0x16b4f7={'value':0x0};let _0x50479d=_0x1d4e5b[0x0];let _0x480aa3=0x1;for(let _0x2df6bd=0x1;_0x2df6bd<_0xbee4b1;++_0x2df6bd){if(_0x1d4e5b[_0x2df6bd]===_0x50479d){++_0x480aa3;}else{if(!appendRLEC(_0x52b3fb,_0x16b4f7,_0x50479d,_0x480aa3))return 0x0;_0x50479d=_0x1d4e5b[_0x2df6bd];_0x480aa3=0x1;}}if(!appendRLEC(_0x52b3fb,_0x16b4f7,_0x50479d,_0x480aa3))return 0x0;return _0x16b4f7['value'];}function pushRLE5(_0x12ff06,_0xc2e55c,_0x53996a){if(_0x53996a<=0x0)return;let _0x21b41c=[HOST_TO_DEVICE_DATA_START,_0x12ff06,0x0];let _0x38f217=pushEBV(_0x21b41c,0x2,_0x53996a);let _0x294cef=Math[_0x1f17('0x9')](_0x53996a*0x5/0x8);pushPackage2(_0x21b41c,0x0,_0x38f217,_0xc2e55c,0x0,_0x294cef);}function appendRLE5(_0x277140,_0x31b2d2,_0x399d07,_0x4ec203){if(_0x4ec203<=0x0)return!![];let _0xbccfc5=Math['floor'](_0x31b2d2['value']*0x5/0x8);let _0x19de4f=0x10-0x1;while(_0x4ec203>0x0){if(_0x4ec203>=SuperBitmapRLE5Length[_0x19de4f]){_0x4ec203-=SuperBitmapRLE5Length[_0x19de4f];let _0x4e0974=_0x19de4f|(_0x399d07?0x10:0x0);_0x31b2d2[_0x1f17('0xc')]=_0x31b2d2[_0x1f17('0xc')]+0x1;if(_0x31b2d2[_0x1f17('0xc')]*0x5>mByteWidth*0x8)return![];switch(_0x31b2d2[_0x1f17('0xc')]&0x7){case 0x0:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974;++_0xbccfc5;break;case 0x1:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974<<0x3;break;case 0x2:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974>>>0x2;++_0xbccfc5;_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|(_0x4e0974&0x3)<<0x6;break;case 0x3:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974<<0x1;break;case 0x4:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974>>>0x4;++_0xbccfc5;_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|(_0x4e0974&0xf)<<0x4;break;case 0x5:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974>>>0x1;++_0xbccfc5;_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|(_0x4e0974&0x1)<<0x7;break;case 0x6:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974<<0x2;break;case 0x7:_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|_0x4e0974>>>0x3;++_0xbccfc5;_0x277140[_0xbccfc5]=_0x277140[_0xbccfc5]|(_0x4e0974&0x7)<<0x5;break;}}else if(_0x4ec203<=0xc){_0x19de4f=_0x4ec203-0x1;}else{--_0x19de4f;}}return!![];}function calcRLE5X(_0x52320a,_0x3b1be3,_0x5390c4){if(_0x3b1be3<=0x0)return 0x0;let _0x1f38ca=0x0,_0x15da60=0x0;let _0x48629b=![];let _0x33c90b=0x80;let _0x191973={'value':0x0};while(!![]){if((_0x52320a[_0x15da60]&_0x33c90b)!==0x0){if(_0x48629b){++_0x1f38ca;}else{if(!appendRLE5(_0x5390c4,_0x191973,![],_0x1f38ca))return 0x0;_0x48629b=!![];_0x1f38ca=0x1;}}else{if(_0x48629b){if(!appendRLE5(_0x5390c4,_0x191973,!![],_0x1f38ca))return 0x0;_0x48629b=![];_0x1f38ca=0x1;}else{++_0x1f38ca;}}if(_0x33c90b===0x1){++_0x15da60;if(_0x15da60>=_0x3b1be3)break;_0x33c90b=0x80;}else{_0x33c90b=_0x33c90b>>>0x1;}}if(_0x48629b&&!appendRLE5(_0x5390c4,_0x191973,!![],_0x1f38ca))return 0x0;return _0x191973['value'];}function calcRLE5D(_0x23ad44,_0x551f08,_0x43490b,_0x10d85c,_0x37bf90){let _0x4a1f51=0x0,_0x3d3d33=0x0;let _0x5d6a71=![];let _0x4f36d0=0x80;let _0x573aa6={'value':0x0};let _0x4ad3a3=Math[_0x1f17('0xf')](_0x551f08,_0x10d85c);if(_0x4ad3a3>0x0){while(!![]){if((_0x43490b[_0x3d3d33]&_0x4f36d0)!==(_0x23ad44[_0x3d3d33]&_0x4f36d0)){if(_0x5d6a71){++_0x4a1f51;}else{if(!appendRLE5(_0x37bf90,_0x573aa6,![],_0x4a1f51))return 0x0;_0x5d6a71=!![];_0x4a1f51=0x1;}}else{if(_0x5d6a71){if(!appendRLE5(_0x37bf90,_0x573aa6,!![],_0x4a1f51))return 0x0;_0x5d6a71=![];_0x4a1f51=0x1;}else{++_0x4a1f51;}}if(_0x4f36d0===0x1){++_0x3d3d33;if(_0x3d3d33>=_0x4ad3a3)break;_0x4f36d0=0x80;}else{_0x4f36d0=_0x4f36d0>>>0x1;}}}if(_0x551f08!==_0x10d85c){if(_0x551f08<_0x10d85c){_0x23ad44=_0x43490b;_0x551f08=_0x10d85c;}_0x4f36d0=0x80;while(!![]){if(0x0!==(_0x23ad44[_0x3d3d33]&_0x4f36d0)){if(_0x5d6a71){++_0x4a1f51;}else{if(!appendRLE5(_0x37bf90,_0x573aa6,![],_0x4a1f51))return 0x0;_0x5d6a71=!![];_0x4a1f51=0x1;}}else{if(_0x5d6a71){if(!appendRLE5(_0x37bf90,_0x573aa6,!![],_0x4a1f51))return 0x0;_0x5d6a71=![];_0x4a1f51=0x1;}else{++_0x4a1f51;}}if(_0x4f36d0===0x1){++_0x3d3d33;if(_0x3d3d33>=_0x551f08)break;_0x4f36d0=0x80;}else{_0x4f36d0=_0x4f36d0>>>0x1;}}}if(_0x5d6a71&&!appendRLE5(_0x37bf90,_0x573aa6,!![],_0x4a1f51))return 0x0;return _0x573aa6[_0x1f17('0xc')];}function pushRLE6(_0x2ca3b3,_0x456a52,_0xcb71c6){if(_0xcb71c6<=0x0)return;let _0xdf4c68=[HOST_TO_DEVICE_DATA_START,_0x2ca3b3,0x0];let _0x32bb8d=pushEBV(_0xdf4c68,0x2,_0xcb71c6);let _0x2da66d=Math[_0x1f17('0x9')](_0xcb71c6*0x6/0x8);pushPackage2(_0xdf4c68,0x0,_0x32bb8d,_0x456a52,0x0,_0x2da66d);}function appendRLE6(_0x40a617,_0x180a01,_0x2e7a9d,_0x4b98e5){if(_0x4b98e5<=0x0)return!![];let _0x2dad72=Math['floor'](_0x180a01[_0x1f17('0xc')]*0x6/0x8);let _0x3d080a=0x20-0x1;while(_0x4b98e5>0x0){if(_0x4b98e5>=SuperBitmapRLE6Length[_0x3d080a]){_0x4b98e5-=SuperBitmapRLE6Length[_0x3d080a];let _0x23acac=_0x3d080a|(_0x2e7a9d?0x20:0x0);_0x180a01[_0x1f17('0xc')]=_0x180a01['value']+0x1;if(_0x180a01[_0x1f17('0xc')]*0x6>mByteWidth*0x8)return![];switch(_0x180a01[_0x1f17('0xc')]&0x3){case 0x0:_0x40a617[_0x2dad72]=_0x40a617[_0x2dad72]|_0x23acac;++_0x2dad72;break;case 0x1:_0x40a617[_0x2dad72]=_0x40a617[_0x2dad72]|_0x23acac<<0x2;break;case 0x2:_0x40a617[_0x2dad72]=_0x40a617[_0x2dad72]|_0x23acac>>>0x4;++_0x2dad72;_0x40a617[_0x2dad72]=_0x40a617[_0x2dad72]|(_0x23acac&0xf)<<0x4;break;case 0x3:_0x40a617[_0x2dad72]=_0x40a617[_0x2dad72]|_0x23acac>>>0x2;++_0x2dad72;_0x40a617[_0x2dad72]=_0x40a617[_0x2dad72]|(_0x23acac&0x3)<<0x6;break;}}else if(_0x4b98e5<=0x14){_0x3d080a=_0x4b98e5-0x1;}else{--_0x3d080a;}}return!![];}function calcRLE6X(_0x594750,_0x3d9634,_0x6e78d0){if(_0x3d9634<=0x0)return 0x0;let _0x80cea6=0x0,_0x429436=0x0;let _0x333264=![];let _0x434955=0x80;let _0x12cec8={'value':0x0};while(!![]){if((_0x594750[_0x429436]&_0x434955)!==0x0){if(_0x333264){++_0x80cea6;}else{if(!appendRLE6(_0x6e78d0,_0x12cec8,![],_0x80cea6))return 0x0;_0x333264=!![];_0x80cea6=0x1;}}else{if(_0x333264){if(!appendRLE6(_0x6e78d0,_0x12cec8,!![],_0x80cea6))return 0x0;_0x333264=![];_0x80cea6=0x1;}else{++_0x80cea6;}}if(_0x434955===0x1){++_0x429436;if(_0x429436>=_0x3d9634)break;_0x434955=0x80;}else{_0x434955=_0x434955>>>0x1;}}if(_0x333264&&!appendRLE6(_0x6e78d0,_0x12cec8,!![],_0x80cea6))return 0x0;return _0x12cec8['value'];}function calcRLE6D(_0x3b6f59,_0x13d647,_0x1e1b3f,_0xe0bdb5,_0x266404){let _0x3c7fa1=0x0,_0x14ac65=0x0;let _0x40b9bd=![];let _0xcd18a4=0x80;let _0x34b547={'value':0x0};let _0xa236a9=Math[_0x1f17('0xf')](_0x13d647,_0xe0bdb5);if(_0xa236a9>0x0){while(!![]){if((_0x1e1b3f[_0x14ac65]&_0xcd18a4)!==(_0x3b6f59[_0x14ac65]&_0xcd18a4)){if(_0x40b9bd){++_0x3c7fa1;}else{if(!appendRLE6(_0x266404,_0x34b547,![],_0x3c7fa1))return 0x0;_0x40b9bd=!![];_0x3c7fa1=0x1;}}else{if(_0x40b9bd){if(!appendRLE6(_0x266404,_0x34b547,!![],_0x3c7fa1))return 0x0;_0x40b9bd=![];_0x3c7fa1=0x1;}else{++_0x3c7fa1;}}if(_0xcd18a4===0x1){++_0x14ac65;if(_0x14ac65>=_0xa236a9)break;_0xcd18a4=0x80;}else{_0xcd18a4=_0xcd18a4>>>0x1;}}}if(_0x13d647!==_0xe0bdb5){if(_0x13d647<_0xe0bdb5){_0x3b6f59=_0x1e1b3f;_0x13d647=_0xe0bdb5;}_0xcd18a4=0x80;while(!![]){if(0x0!==(_0x3b6f59[_0x14ac65]&_0xcd18a4)){if(_0x40b9bd){++_0x3c7fa1;}else{if(!appendRLE6(_0x266404,_0x34b547,![],_0x3c7fa1))return 0x0;_0x40b9bd=!![];_0x3c7fa1=0x1;}}else{if(_0x40b9bd){if(!appendRLE6(_0x266404,_0x34b547,!![],_0x3c7fa1))return 0x0;_0x40b9bd=![];_0x3c7fa1=0x1;}else{++_0x3c7fa1;}}if(_0xcd18a4===0x1){++_0x14ac65;if(_0x14ac65>=_0x13d647)break;_0xcd18a4=0x80;}else{_0xcd18a4=_0xcd18a4>>>0x1;}}}if(_0x40b9bd&&!appendRLE6(_0x266404,_0x34b547,!![],_0x3c7fa1))return 0x0;return _0x34b547['value'];}function pushEBV(_0x38f939,_0x22e4e9,_0x2da946){if(_0x2da946>=0xc0){_0x38f939[_0x22e4e9+0x0]=_0x2da946>>>0x8|0xc0;_0x38f939[_0x22e4e9+0x1]=_0x2da946&0xff;return _0x22e4e9+0x2;}else{_0x38f939[_0x22e4e9+0x0]=_0x2da946;return _0x22e4e9+0x1;}}function pushPackage(_0x2b384d){pushPackage2(_0x2b384d,0x0,_0x2b384d['length'],null,0x0,0x0);}function pushPackage2(_0x2feaae,_0x1c3bb0,_0xaa61e,_0x122a50,_0x301206,_0x4936cd){let _0x55bc9d='',_0x4c76ca,_0xba9f5f;for(_0x4c76ca=_0x1c3bb0;_0x4c76ca<_0xaa61e;++_0x4c76ca){_0xba9f5f=parseInt(_0x2feaae[_0x4c76ca]);_0xba9f5f=_0xba9f5f[_0x1f17('0x4')](0x10);if((_0xba9f5f[_0x1f17('0x6')]&0x1)===0x1){_0x55bc9d+='0'+_0xba9f5f;}else{_0x55bc9d+=_0xba9f5f;}}for(_0x4c76ca=_0x301206;_0x4c76ca<_0x4936cd;++_0x4c76ca){_0xba9f5f=parseInt(_0x122a50[_0x4c76ca]);_0xba9f5f=_0xba9f5f['toString'](0x10);if((_0xba9f5f[_0x1f17('0x6')]&0x1)===0x1){_0x55bc9d+='0'+_0xba9f5f;}else{_0x55bc9d+=_0xba9f5f;}}mArrayBitmap['push'](_0x55bc9d);}module[_0x1f17('0x0')]={'arrayWithImage':arrayWithImage,'arrayWithParam':arrayWithParam};function allocIntArray(_0x5021ec){let _0x2190c3=new Array(_0x5021ec);for(--_0x5021ec;_0x5021ec>=0x0;--_0x5021ec){_0x2190c3[_0x5021ec]=0x0;}return _0x2190c3;}function checkArrayEquals(_0x35b7bc,_0x446848,_0x3b2b2d){for(let _0xa48d23=0x0;_0xa48d23<_0x3b2b2d;++_0xa48d23){if(_0x35b7bc[_0xa48d23]!==_0x446848[_0xa48d23])return![];}return!![];}