import printingControl from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073";import supVanPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import bleToothManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{'\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068':50,"matHeight":30,"dpiValue":8,"imageRgbaData":[],'\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074':[],'\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':0,'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074':0,"printFirstType":false,'\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074':[],'\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061':null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'','\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,async initEncodeData(objectData,imageRgbaData){try{var _0x5f20dc=(947850^947848)+(873188^873189);const that=this;_0x5f20dc=(422425^422424)+(245641^245644);that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?916344^916345:619031^619031;await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},initializeData(){const that=this;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(511338^511338)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=204601^204600;}var _0x52g39d;let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(576758^576756))*(544603^544513);_0x52g39d=640838^640838;let object={'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],"Width":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0048\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065':rotateAngle,'\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(767931^767935)+(410825^410827)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"VerticalNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(744763^744767),'\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],"DeviceSn":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};if(bleTool['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){console['\u006C\u006F\u0067']("tcejbo".split("").reverse().join(""),object);imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);}else{object['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(976061^976057);console['\u006C\u006F\u0067']("\u006F\u0062\u006A\u0065\u0063\u0074",object);imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);}that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{const that=this;var _0x6feceb=(981716^981713)+(435057^435056);let bufferTransferCount=151204^151204;_0x6feceb='\u0063\u006C\u0061\u006D\u0062\u006E';let num=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u006F\u0070\u0069\u0065\u0073'];var _0x4b85fc;let _bufLength=856510^852414;_0x4b85fc=(815261^815258)+(545801^545792);let countBuff=new Array();var _0x1325ef;let isEndFlag=!![];_0x1325ef='\u006E\u0064\u0062\u0066\u006F\u0065';let imgTotalCount=269017^269016;var _0x86e23f=(801890^801892)+(786709^786716);let marginleft=imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']();_0x86e23f='\u006B\u006C\u006F\u0065\u0062\u006C';console['\u006C\u006F\u0067']("\u8DDD\u8FB9\u5DE6\u7247\u56FE".split("").reverse().join(""),marginleft);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft;console['\u006C\u006F\u0067']("\u6570\u5217\u7247\u56FE".split("").reverse().join(""),_nColumnTotalCnt);var _0x59aa=(804171^804169)+(334555^334555);let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(580235^580236))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0x59aa=766268^766264;console['\u006C\u006F\u0067']("\u6BCF\u5217\u5B57\u8282\u6570",nBytePerLine);let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(331294^331272))/nBytePerLine);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u7F13\u51B2\u533A\u6700\u5927\u5217\u6570",nMax);let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(784985^784984))/nMax);console['\u006C\u006F\u0067']("\u6BCF\u4E2A\u56FE\u7247\u7F13\u51B2\u533A\u6570\u91CF\u0020\u0062\u0075\u0066\u0066\u0065\u0072\u0043\u006F\u0075\u006E\u0074\u0049\u006D\u0061\u0067\u0065",bufferCountImage);var _0x47f37e=(835017^835018)+(210041^210042);let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();_0x47f37e=(595173^595172)+(774211^774219);console['\u006C\u006F\u0067']("\u5EA6\u957Fsetyblla".split("").reverse().join(""),allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=738416^738416;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=945051^945051;i<bufferCountImage;i++){var _0x186cc=(215099^215098)+(434015^434015);let _btBuf=new Uint8Array(_bufLength);_0x186cc=(190097^190104)+(679470^679463);pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](894651^894650);if(i==(528278^528278)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](627947^627946);}var _0xeaaeda;let bufferColumnCnt=647005^647005;_0xeaaeda='\u0063\u006A\u0065\u006F\u0061\u0064';if(i==bufferCountImage-(286459^286458)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](950405^950404);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](966043^966042);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}var _0xf_0xc1d=(926207^926202)+(948707^948704);let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;_0xf_0xc1d=746128^746133;let end=star+bufferColumnCnt*nBytePerLine;var _0xa2f6de;let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_0xa2f6de="enpljp".split("").reverse().join("");_btBuf[683687^683683]=bufferColumnCnt&(372333^372370);_btBuf[285110^285107]=bufferColumnCnt>>(750241^750249)&(527234^527229);for(var y=155181^155181;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(445376^445390)+y]=b[y];}let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](815314^815316);for(var z=930754^930752;z<(211369^211373);z++){_btBuf[z]=btdata[z-(335864^335866)];}_btBuf[243433^243439]=nBytePerLine&(750302^750113);if(marginleft>(193897^193897)){_btBuf[191984^191992]=marginleft&(544316^544451);_btBuf[192861^192852]=marginleft>>(763953^763961)&(717087^717280);}else{_btBuf[209431^209439]=765877^765876;_btBuf[556325^556332]=571724^571724;}_btBuf[214671^214661]=(284974^284975)&(352332^352435);_btBuf[910089^910082]=740877^740877;_btBuf[197516^197504]=712308^712308;_btBuf[779084^779073]=320140^320140;var _0x9c51bb=(823090^823099)+(527322^527323);let len=_btBuf[426307^426310];_0x9c51bb=(261214^261215)+(576754^576753);len<<=345318^345326;len+=_btBuf[794923^794927];len*=_btBuf[779363^779365];len+=680511^680497;var _0x1aa1cf=(199577^199578)+(153579^153582);let un=738490^738490;_0x1aa1cf=304749^304749;for(var j=758869^758871;j<(221258^221252);j++){un+=_btBuf[j];}let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(796087^795831));if(x>(953807^953807)){for(var k=970558^970558;k<x;k++){un+=_btBuf[(k+(404458^404459))*(268313^268569)-(549063^549062)];}}_btBuf[357318^357318]=un;_btBuf[563105^563104]=un>>(469794^469802);countBuff['\u0070\u0075\u0073\u0068'](_btBuf);console['\u006C\u006F\u0067']("rp".split("").reverse().join(""),pr);}}while(bufferTransferCount<bufferCountImage*num&&countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']>(425113^425113)){var _0xfcffc;let sendData=null;_0xfcffc=(602810^602802)+(746864^746870);let bufferCount=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);let bufferCountOne=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);do{var _0xf3b53f=(465849^465849)+(471341^471343);let bufferPackage=Array();_0xf3b53f=(273838^273831)+(512204^512205);for(let a=855136^855136;a<bufferCount;a++){for(var b=118451^118451;b<_bufLength;b++){bufferPackage[b+a*_bufLength]=countBuff[a][b];}}try{this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=[];this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=bufferPackage;sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](bufferPackage);}catch(error){console['\u006C\u006F\u0067']("\u8D25\u5931\u7F29\u538B".split("").reverse().join(""));sendData=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];num=470512^470512;throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}bufferCount--;}while(sendData['\u004C\u0065\u006E\u0067\u0074\u0068']>_bufLength);bufferTransferCount=bufferTransferCount+bufferCount+(517571^517570);countBuff['\u0073\u0070\u006C\u0069\u0063\u0065'](806169^806169,bufferCountOne);if(sendData['\u006C\u0065\u006E\u0067\u0074\u0068']>(881030^881030)){await that['\u006F\u006E\u0044\u0061\u0074\u0061\u0050\u0072\u006F\u0063\u0065\u0073\u0073\u0069\u006E\u0067'](sendData);}}}catch(error){throw error;}},async onDataProcessing(data){try{var _0xc707f=(870942^870937)+(365667^365664);const that=this;_0xc707f=254174^254170;let sendData=data;let type=false;for(var i=322111^322110;i<(379079^379075);i++){let dataIndex3=sendData[i*(103636^103508)+(433838^433837)];let dataIndex2=sendData[i*(771564^771436)+(272071^272069)];let dlen=dataIndex3<<(420344^420336)|dataIndex2;dlen=dlen+(995051^995055);var _0xab4ged=(636942^636942)+(665606^665603);let opcode=sendData[i*(886358^886486)+(618020^618018)];_0xab4ged=283487^283483;var _0x9ffcc;let dataIndex6=sendData[i*(122573^122445)+(764391^764386)];_0x9ffcc="kankld".split("").reverse().join("");if(dlen>(739034^738906)&&(opcode==(172345^172418)||opcode==(790190^790184)||opcode==(983907^983972)||opcode==(384969^384974))&&dataIndex6==(302475^302369)){this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(977349^977221)]=this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(942259^942131)]+(235646^235647);type=!![];console['\u006C\u006F\u0067']("\u8FDB\u5165\u9519\u8BEF\u003A\u0020","\u91CD\u65B0\u538B\u7F29");}}if(type){sendData=[];console['\u006C\u006F\u0067']("\u518D\u6B21\u538B\u7F29");try{sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}}that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}catch(error){throw error;}},doPrint(){const that=this;if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(327125^327125)){supVanPrintUtils['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u6253\u5370\u5B57\u6A21\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A"));}}else{supVanPrintUtils['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'][584864^584864]);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=589896^589896;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};