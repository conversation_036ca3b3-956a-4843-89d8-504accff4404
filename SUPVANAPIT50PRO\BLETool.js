import supPrintUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import supPrintUtilsMP50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import supPrintUtilsG15 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import supPrintUtilsG21 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";import canvasDataRGBAUtils from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0061\u0074\u0061\u0052\u0047\u0042\u0041\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import lpapi from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u004C\u0050\u0041\u0050\u0049\u002F\u004C\u0050\u0041\u0050\u0049\u002E\u006A\u0073";export default{"CMD_INQUIRY_STA":0x11,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0044\u0050\u0049':0x22,"SUPPORT_SERVICE_UUID":["\u0046\u0045\u0045\u0037","\u0045\u0030\u0046\u0046"],"SERVICE_UUID":"E0FF",'\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044':"\u0046\u0046\u0045\u0031",'\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044':"\u0046\u0046\u0045\u0039","deviceId":'','\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':'','\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064':'','\u0077\u0072\u0069\u0074\u0065\u0049\u0064':'','\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074':[],'\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065':false,"deviceSn":'',"dpiValue":8,"isCustomType":false,wxPromise(api,options={}){return new Promise((resolve,reject)=>{api({...options,"success":res=>resolve(res),"fail":e=>reject(e)});});},async scanBleDeviceList(callBack){var _0x045c1b;const that=this;_0x045c1b=539214^539213;that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']=[];var _0xbgf1c=(659466^659459)+(190012^190014);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0031'];_0xbgf1c=(426248^426250)+(576694^576693);try{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006F\u0070\u0065\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072']);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0032'];var _0x2964e=(346377^346378)+(609211^609209);const stateRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0041\u0064\u0061\u0070\u0074\u0065\u0072\u0053\u0074\u0061\u0074\u0065']);_0x2964e='\u006C\u006C\u006C\u006D\u0064\u006A';resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0033'];if(!stateRes&&!stateRes['\u0061\u0076\u0061\u0069\u006C\u0061\u0062\u006C\u0065']){resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0035'];return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u7528\u53EF\u4E0D\u5668\u914D\u9002\u7259\u84DD".split("").reverse().join(""));}await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u0061\u0072\u0074\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079'],{'\u0061\u006C\u006C\u006F\u0077\u0044\u0075\u0070\u006C\u0069\u0063\u0061\u0074\u0065\u0073\u004B\u0065\u0079':!![]});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0034'];wx['\u006F\u006E\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0046\u006F\u0075\u006E\u0064'](res=>{if(res&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][921121^921121]&&res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][680537^680537]['\u006E\u0061\u006D\u0065']&&that['\u0061\u006C\u006C\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][425635^425635]['\u006E\u0061\u006D\u0065'])){if(!that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0069\u006E\u0063\u006C\u0075\u0064\u0065\u0073'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][694763^694763]['\u006E\u0061\u006D\u0065'])){that['\u0073\u0065\u0061\u0072\u0063\u0068\u0042\u006C\u0075\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](res['\u0064\u0065\u0076\u0069\u0063\u0065\u0073'][265288^265288]['\u006E\u0061\u006D\u0065']);callBack(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073'](res));}}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async stopScanBleDevices(){try{await this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0074\u006F\u0070\u0042\u006C\u0075\u0065\u0074\u006F\u006F\u0074\u0068\u0044\u0065\u0076\u0069\u0063\u0065\u0073\u0044\u0069\u0073\u0063\u006F\u0076\u0065\u0072\u0079']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"msg":"\u505C\u6B62\u641C\u7D22\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}catch(error){var _0xe3843b;let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0037'];_0xe3843b=251339^251337;throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async disconnectBleDevice(){var _0x36ffe;const that=this;_0x36ffe=633209^633214;try{if(that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']){if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await lpapi['\u0063\u006C\u006F\u0073\u0065\u0050\u0072\u0069\u006E\u0074\u0065\u0072']();return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u65AD\u5F00\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}else{await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u006C\u006F\u0073\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{"deviceId":that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"msg":"\u65AD\u5F00\u84DD\u7259\u8BBE\u5907\u6210\u529F"});}}else{var _0xbg59dd;let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0037'];_0xbg59dd=(676086^676086)+(580098^580102);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,"\u84DD\u7259\u8BBE\u5907\u53F7\u4E3A\u7A7A");}}catch(error){console['\u006C\u006F\u0067'](error);var _0xd875c=(309615^309614)+(928984^928987);let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0036'];_0xd875c=(650572^650572)+(233964^233960);throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},async connectBleDevice(nBleDeviceInfo){try{const that=this;let bleDeviceInfo=nBleDeviceInfo;if(bleDeviceInfo){that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=bleDeviceInfo['\u006E\u0061\u006D\u0065'];if(that['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){if(bleDeviceInfo['\u006E\u0061\u006D\u0065']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0044\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u006E\u0061\u006D\u0065']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'连接德佟蓝牙设备成功'});}}else{if(bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']){await this['\u0063\u006F\u006E\u006E\u0065\u0063\u0074\u0053\u0075\u0070\u0076\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065'](bleDeviceInfo['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']);return constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"msg":"\u8FDE\u63A5\u7855\u65B9\u84DD\u7259\u8BBE\u5907\u6210\u529F","serviceUuid":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});}}}}catch(error){throw error;}},async connectDtBleDevice(nBluetoothName){var _0x6b97da;let bluetoothName=nBluetoothName;_0x6b97da=123634^123633;return new Promise((resolve,reject)=>{lpapi['\u006F\u0070\u0065\u006E\u0050\u0072\u0069\u006E\u0074\u0065\u0072'](bluetoothName,res=>{resolve(res);},error=>{reject(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0030\u0039'],error));});});},async connectSupvanBleDevice(nDeviceId){let resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0030'];try{const that=this;var _0xe66a6a=(141087^141085)+(422511^422506);let deviceId=nDeviceId;_0xe66a6a='\u006B\u0067\u006B\u006D\u006A\u0063';that['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']=deviceId;that['\u0073\u0065\u0074\u0050\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C'](wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u006D\u006F\u0064\u0065\u006C']);await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0072\u0065\u0061\u0074\u0065\u0042\u004C\u0045\u0043\u006F\u006E\u006E\u0065\u0063\u0074\u0069\u006F\u006E'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0031'];that['\u0062\u006C\u0065\u004D\u0074\u0075']();await that['\u0073\u0074\u006F\u0070\u0053\u0063\u0061\u006E\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073']();resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0032'];const bleDeviceServicesRes=await that['\u0067\u0065\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073']();resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0033'];bleDeviceServicesRes['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0073']['\u006D\u0061\u0070'](i=>{for(const s of this['\u0053\u0055\u0050\u0050\u004F\u0052\u0054\u005F\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']){if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(728375^728383)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](886595^886599,917890^917898)==s){this['\u0075\u0070\u0064\u0061\u0074\u0061\u0042\u006C\u0065\u0055\u0075\u0069\u0064'](s);this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}}});var _0xab91f=(209064^209056)+(619659^619658);const bleDeviceCharacteristicsRes=await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073'],{"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"serviceId":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064']});_0xab91f=(355883^355887)+(437729^437737);resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0034'];bleDeviceCharacteristicsRes['\u0063\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0073']['\u006D\u0061\u0070'](i=>{if(i&&i['\u0075\u0075\u0069\u0064']&&i['\u0075\u0075\u0069\u0064']['\u006C\u0065\u006E\u0067\u0074\u0068']>=(136416^136424)){if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](899270^899266,963944^963936)==this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']){this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}else if(i['\u0075\u0075\u0069\u0064']['\u0074\u006F\u0053\u0074\u0072\u0069\u006E\u0067']()['\u0074\u006F\u0055\u0070\u0070\u0065\u0072\u0043\u0061\u0073\u0065']()['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](245894^245890,213289^213281)==this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']){this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064']=i['\u0075\u0075\u0069\u0064'];}}});await that['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u006E\u006F\u0074\u0069\u0066\u0079\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'],{'\u0073\u0074\u0061\u0074\u0065':!![],'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],'\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],"characteristicId":this['\u006E\u006F\u0074\u0069\u0066\u0079\u0049\u0064']});resultCode=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0035'];wx['\u006F\u006E\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0043\u0068\u0061\u006E\u0067\u0065'](res=>{if(that['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtils['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsMP50['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG15['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}else if(that['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){supPrintUtilsG21['\u006E\u006F\u0074\u0069\u0066\u0079\u0044\u0061\u0074\u0061'](new Uint8Array(res['\u0076\u0061\u006C\u0075\u0065']));}});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](resultCode,error);}},updataBleUuid(currentServiceUuid){switch(currentServiceUuid){case"\u0046\u0045\u0045\u0037":this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0045\u0037";this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0043\u0031";this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="\u0046\u0045\u0043\u0031";break;case"FF0E".split("").reverse().join(""):this['\u0053\u0045\u0052\u0056\u0049\u0043\u0045\u005F\u0055\u0055\u0049\u0044']="\u0045\u0030\u0046\u0046";this['\u004E\u004F\u0054\u0049\u0046\u0059\u005F\u0055\u0055\u0049\u0044']="\u0046\u0046\u0045\u0031";this['\u0057\u0052\u0049\u0054\u0045\u005F\u0055\u0055\u0049\u0044']="9EFF".split("").reverse().join("");break;default:break;}},async stopPrint(callback){try{const that=this;if(this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](!![]);}else{canvasDataRGBAUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0044\u0074'](false);supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtils['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsMP50['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG15['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0070\u0076\u0061\u006E']();supPrintUtilsG21['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](callback);}}catch(error){throw error;}},async bleMtu(){if(wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']()['\u0070\u006C\u0061\u0074\u0066\u006F\u0072\u006D']=="diordna".split("").reverse().join("")){await new Promise(resolve=>setTimeout(resolve,193561^194545));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0073\u0065\u0074\u0042\u004C\u0045\u004D\u0054\u0055'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"mtu":182});}return Promise['\u0072\u0065\u0073\u006F\u006C\u0076\u0065'];},async getBleDeviceServices(){await new Promise(resolve=>setTimeout(resolve,461324^462800));return this['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0067\u0065\u0074\u0042\u004C\u0045\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u0065\u0072\u0076\u0069\u0063\u0065\u0073'],{'\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064':this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064']});},onWriteBLECharacteristicValue(nbyteData){var _0x22804g;let byteData=nbyteData;_0x22804g=(871226^871218)+(342179^342177);console['\u006C\u006F\u0067']("\u53D1\u9001\u6570\u636E\u003A",byteData);if(byteData){var _0x2fcg9b=(547895^547902)+(329751^329746);const buffer=new ArrayBuffer(byteData['\u006C\u0065\u006E\u0067\u0074\u0068']);_0x2fcg9b="njljbm".split("").reverse().join("");const dataView=new DataView(buffer);for(var i=733668^733668;i<byteData['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){dataView['\u0073\u0065\u0074\u0055\u0069\u006E\u0074\u0038'](i,byteData[i]);}wx['\u0077\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065']({"deviceId":this['\u0064\u0065\u0076\u0069\u0063\u0065\u0049\u0064'],"serviceId":this['\u0073\u0065\u0072\u0076\u0069\u0063\u0065\u0049\u0064'],"characteristicId":this['\u0077\u0072\u0069\u0074\u0065\u0049\u0064'],'\u0076\u0061\u006C\u0075\u0065':dataView['\u0062\u0075\u0066\u0066\u0065\u0072'],'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>{},'\u0066\u0061\u0069\u006C':res=>{console['\u006C\u006F\u0067']("\u0077\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065\u0020\u0066\u0061\u0069\u006C",res);}});}},t50ProBleDevices(nBluetoothName){const bluetoothName=nBluetoothName;var _0xd9g1f=(491205^491206)+(237219^237227);const validPrefixes=["\u0054\u0030\u0031\u0030\u0030\u0041","\u0054\u0030\u0030\u0039\u0039\u0041","\u0054\u0030\u0030\u0034\u0036\u0041","\u0054\u0030\u0030\u0031\u0033\u0042","\u0054\u0030\u0030\u0032\u0030\u0042","\u0054\u0030\u0030\u0032\u0034\u0042","\u0054\u0030\u0030\u0039\u0037\u0041","\u0054\u0030\u0031\u0030\u0031\u0041","\u0054\u0030\u0031\u0031\u0034\u0041","\u0054\u0030\u0031\u0031\u0035\u0041","\u0054\u0030\u0031\u0031\u0032\u0041","\u0054\u0030\u0031\u0031\u0033\u0041","\u0054\u0030\u0031\u0034\u0035\u0042","\u0054\u0030\u0031\u0034\u0036\u0042","\u0054\u0030\u0031\u0034\u0039\u0042","B0510T".split("").reverse().join(""),"B1510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0032\u0042","\u0054\u0030\u0031\u0035\u0033\u0042","B4510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0035\u0035\u0042","B6510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0030\u0033\u0042","\u0054\u0030\u0030\u0032\u0031\u0042","B9510T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0030\u0042","B1610T".split("").reverse().join(""),"B7610T".split("").reverse().join(""),"\u0054\u0030\u0031\u0036\u0038\u0042","B9610T".split("").reverse().join(""),"B2610T".split("").reverse().join(""),"B3610T".split("").reverse().join(""),"B5810T".split("").reverse().join("")];_0xd9g1f=(958182^958177)+(417010^417010);if(bluetoothName){return validPrefixes['\u0073\u006F\u006D\u0065'](prefix=>bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068'](prefix));}return false;},t80BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0030\u0033\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0031\u0031\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0030\u0039\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0037\u0035\u0041")){return!![];}else{return false;}}},t50ProAndT80BleDevices(nBluetoothName){var _0xc185f;let bluetoothName=nBluetoothName;_0xc185f='\u0064\u0066\u0066\u0068\u0063\u006D';if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},mp50BleDevices(nBluetoothName){var _0x4831b=(425282^425284)+(251082^251074);let bluetoothName=nBluetoothName;_0x4831b='\u0064\u006B\u006B\u0064\u0062\u0067';if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3000PM".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A4000PM".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0035\u0041")){return!![];}else{return false;}}},mp50OnlyBleDevices(nBluetoothName){var _0x69008g=(338829^338825)+(540124^540122);let bluetoothName=nBluetoothName;_0x69008g=(676469^676468)+(124921^124921);if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0030\u0030\u0030\u0033\u0041")){return!![];}else{return false;}}},dtBleDevices(nBluetoothName){var _0xa4559b=(221040^221048)+(464374^464371);let bluetoothName=nBluetoothName;_0xa4559b=831222^831218;if(bluetoothName){if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0035\u0030")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0038\u0030")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u004D\u0050\u0035\u0030")){return!![];}else{return false;}}},g15BleDevices(nBluetoothName){var _0x631a1e=(915782^915790)+(592168^592171);let bluetoothName=nBluetoothName;_0x631a1e=(345143^345151)+(597416^597419);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A3100G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4100G".split("").reverse().join(""))){return!![];}else{return false;}},e11BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0033\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A9310T".split("").reverse().join(""))){return!![];}else{return false;}},g11BleDevices(nBluetoothName){var _0xbe13eb=(998117^998113)+(694755^694763);let bluetoothName=nBluetoothName;_0xbe13eb=256688^256689;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A2200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0031\u0041")){return!![];}else{return false;}},g11ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A8200G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0038\u0042")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5400G".split("").reverse().join(""))){return!![];}else{return false;}},g11MBleDevices(nBluetoothName){var _0xc34cbd=(604689^604697)+(180797^180799);let bluetoothName=nBluetoothName;_0xc34cbd=(198299^198303)+(865409^865417);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0033\u0041")){return!![];}else{return false;}},g11MPlusBleDevices(nBluetoothName){var _0xg2b6a;let bluetoothName=nBluetoothName;_0xg2b6a=(327293^327285)+(604412^604411);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5200G".split("").reverse().join(""))){return!![];}else{return false;}},g15MBleDevices(nBluetoothName){var _0x5b_0xcc8;let bluetoothName=nBluetoothName;_0x5b_0xcc8=681432^681434;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("4000G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0037\u0041")){return!![];}else{return false;}},g15MProBleDevices(nBluetoothName){var _0xfcbc=(702395^702398)+(835097^835098);let bluetoothName=nBluetoothName;_0xfcbc="hnbmcg".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0038\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0038\u0042")){return!![];}else{return false;}},g15MiniBleDevices(nBluetoothName){var _0x1e_0xcc9=(521672^521664)+(692687^692683);let bluetoothName=nBluetoothName;_0x1e_0xcc9=(121597^121598)+(784974^784967);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0030\u0033\u0041")){return!![];}else{return false;}},g15ProBleDevices(nBluetoothName){var _0x4b3g5c;let bluetoothName=nBluetoothName;_0x4b3g5c="pjqfjk".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A5100G".split("").reverse().join(""))||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0031\u0036")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("B5100G".split("").reverse().join(""))){return!![];}else{return false;}},g18ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0032\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0032\u0042")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0034")){return!![];}else{return false;}},g19MPlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0037\u0041")){return!![];}else{return false;}},g19PlusBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0036\u0041")){return!![];}else{return false;}},g28BleDevices(nBluetoothName){var _0xd4ge4c;let bluetoothName=nBluetoothName;_0xd4ge4c=(634943^634937)+(798818^798827);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0033\u0041")){return!![];}else{return false;}},q11PlusBleDevices(nBluetoothName){var _0x6a82d=(632881^632884)+(231083^231074);let bluetoothName=nBluetoothName;_0x6a82d='\u0069\u0062\u0065\u0063\u0064\u0064';if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A1100D".split("").reverse().join(""))){return!![];}else{return false;}},q11ProBleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0033\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("B3100D".split("").reverse().join(""))){return!![];}else{return false;}},q15BleDevices(nBluetoothName){var _0x815fa;let bluetoothName=nBluetoothName;_0x815fa="dnfbhn".split("").reverse().join("");if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0038")){return!![];}else{return false;}},q15MiniBleDevices(nBluetoothName){var _0x878cf=(119479^119476)+(160783^160783);let bluetoothName=nBluetoothName;_0x878cf=(986804^986801)+(647785^647776);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0034\u0041")){return!![];}else{return false;}},q15ProBleDevices(nBluetoothName){var _0x7c4afd;let bluetoothName=nBluetoothName;_0x7c4afd=605597^605593;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0030\u0039\u0041")){return!![];}else{return false;}},q18BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("A0100D".split("").reverse().join(""))){return!![];}else{return false;}},q19PlusBleDevices(nBluetoothName){var _0xf2eb5d=(253632^253634)+(793616^793623);let bluetoothName=nBluetoothName;_0xf2eb5d=(347820^347822)+(959994^959995);if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0032\u0041")){return!![];}else{return false;}},g21BleDevices(nBluetoothName){let bluetoothName=nBluetoothName;if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0034\u0041")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0032\u0039")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0044\u0030\u0030\u0031\u0034")||bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0047\u0030\u0030\u0034\u0030")){return!![];}else{return false;}},gSeriesBleDevices(nBluetoothName){var _0x8f_0x131;let bluetoothName=nBluetoothName;_0x8f_0x131=(704935^704931)+(911869^911866);if(this['\u0067\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0065\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0031\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0038\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0031\u0039\u004D\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0031\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u004D\u0069\u006E\u0069\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0035\u0050\u0072\u006F\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0071\u0031\u0039\u0050\u006C\u0075\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}},allBleDevices(nBluetoothName){var _0xed3f=(590891^590888)+(682817^682824);let bluetoothName=nBluetoothName;_0xed3f='\u0068\u0069\u0064\u006C\u006E\u0064';if(bluetoothName){if(this['\u0069\u0073\u0043\u0075\u0073\u0074\u006F\u006D\u0054\u0079\u0070\u0065']){if(this['\u0054\u0035\u0030\u0050\u0072\u006F\u0043\u0075\u0073\u0074\u006F\u006D\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}else{if(this['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}}},g21OrG28BleDevices(nBluetoothName){var _0x98881g=(618362^618361)+(215327^215326);let bluetoothName=nBluetoothName;_0x98881g=494499^494501;if(bluetoothName){if(this['\u0067\u0032\u0031\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)||this['\u0067\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){return!![];}else{return false;}}},T50ProCustomBleDevices(nBluetoothName){var _0x1f33d=(831674^831667)+(874717^874709);let bluetoothName=nBluetoothName;_0x1f33d=149664^149664;if(bluetoothName){var _0x8fcd=(456340^456343)+(972765^972764);const len=bluetoothName['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x8fcd=484406^484406;if(len['\u006C\u0065\u006E\u0067\u0074\u0068']<(739846^739849)){return false;}if(bluetoothName['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("\u0054\u0030\u0031\u0034\u0035\u0042")){var _0x144f=(365083^365087)+(763047^763055);const len=bluetoothName['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x144f=748928^748936;if(bluetoothName[len-(583996^583992)]==="\u0044"){return!![];}return false;}else{return false;}}},setPhoneModel(nPhoneModel){var _0x6d97eg=(408448^408450)+(522901^522901);let phoneModel=nPhoneModel;_0x6d97eg=212853^212850;if(phoneModel){if(phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("e9 CC IM".split("").reverse().join(""))||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("00MVDP".split("").reverse().join(""))||phoneModel['\u0073\u0074\u0061\u0072\u0074\u0073\u0057\u0069\u0074\u0068']("00MABP".split("").reverse().join(""))){this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=!![];}else{this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065']=false;}}},getPhoneModel(){return this['\u0070\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C\u0054\u0079\u0070\u0065'];},getFDpiValue(){return this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},setFDpiValue(nValue){var _0x69a7a;const that=this;_0x69a7a=(476301^476298)+(834934^834928);var _0x1a3e=(697375^697372)+(122240^122246);let value=nValue;_0x1a3e=(100258^100261)+(609440^609440);let bluetoothName=that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];if(bluetoothName){if(this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=value;}else if(this['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=value;}else{this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=545577^545569;}}},getMaxDotValueType(nBluetoothName){let bluetoothName=nBluetoothName;if(this['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](bluetoothName)){if(this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']==(171884^171872)){return!![];}return false;}else{return false;}},setCustomType(custom){this['\u0069\u0073\u0043\u0075\u0073\u0074\u006F\u006D\u0054\u0079\u0070\u0065']=custom;}};