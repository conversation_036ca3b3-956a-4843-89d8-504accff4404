import bleManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";export default{'\u0062\u0074':[],'\u0077\u0069\u0064\u0074\u0068':0,"height":0,'\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070':0,"marginBottom":0,'\u0070\u0072\u0069\u006E\u0074\u0065\u0072\u0053\u006E':0,'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':12,getAllBytes(objectData){console['\u006C\u006F\u0067']("\u0031\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061",objectData);var _0x2d057f=(370025^370031)+(550503^550499);const that=this;_0x2d057f='\u0062\u0061\u006F\u0061\u006B\u0069';var _0x67a90a;let imageRgbaData=objectData['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'];_0x67a90a=913110^913107;let imageWidth=objectData['\u0057\u0069\u0064\u0074\u0068'];let imageHeight=objectData['\u0048\u0065\u0069\u0067\u0068\u0074'];var _0x_0xe79=(620420^620428)+(648180^648178);let rotate=objectData['\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065'];_0x_0xe79=(892241^892249)+(713084^713080);let offsetX=objectData['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D'];let offsetY=objectData['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D'];let flipType=objectData['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'];var _0x3d_0x5b4=(924620^924617)+(221259^221263);let printerSn=objectData['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];_0x3d_0x5b4=(201929^201935)+(655175^655168);that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleManage['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("\u0034\u0074\u0068\u0061\u0074\u002E\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065",that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);if(!imageWidth){imageWidth=981376^981377;}if(!imageHeight){imageHeight=835690^835691;}if(!rotate){rotate=362991^362990;}if(!offsetX){offsetX=791377^791377;}if(!offsetY){offsetY=242018^242018;}if(!flipType){flipType=false;}if(!printerSn){printerSn='';}var _0x4a7c;let arr=new Array(imageHeight);_0x4a7c='\u006B\u0065\u006E\u006B\u0068\u006F';for(let i=147666^147666;i<imageHeight;i++){let rowArr=new Array(imageWidth);for(let j=930549^930549;j<imageWidth;j++){var _0x6cc=(785549^785551)+(827919^827910);let index=(i*imageWidth+j)*(285955^285959);_0x6cc=(139022^139023)+(193405^193397);var _0xddb0c=(152494^152487)+(461734^461734);let r=imageRgbaData[index];_0xddb0c="oniehl".split("").reverse().join("");var _0xe72ec=(764344^764337)+(766746^766747);let g=imageRgbaData[index+(359455^359454)];_0xe72ec=(461474^461475)+(644728^644730);let b=imageRgbaData[index+(362140^362142)];var _0xeefd;let a=imageRgbaData[index+(555659^555656)];_0xeefd="mckgel".split("").reverse().join("");let value=this['\u0063\u006C\u0061\u006D\u0070'](r,g,b,a);rowArr[j]=value;}arr[i]=rowArr;}if(rotate!=(157616^157616)){arr=this['\u0072\u006F\u0074\u0061\u0074\u0065\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight,rotate);if(rotate==(593049^593091)||rotate==(201778^202044)||rotate==-(154809^154851)){console['\u006C\u006F\u0067']("\u8F6C\u65CB".split("").reverse().join(""),rotate);var _0x5d3f8e=(961968^961970)+(121943^121937);let temp=imageWidth;_0x5d3f8e=(933062^933063)+(700956^700959);imageWidth=imageHeight;imageHeight=temp;}}console['\u006C\u006F\u0067']("\u7FFB\u8F6C\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003E",flipType);if(flipType>(130092^130092)){arr=this['\u0066\u006C\u0069\u0070\u0044\u0061\u0074\u0061'](arr,925246^925247);arr=this['\u0066\u006C\u0069\u0070\u0044\u0061\u0074\u0061'](arr,786636^786638);}if(offsetX!=(523201^523201)||offsetY!=(607269^607269)){arr=this['\u006F\u0066\u0066\u0073\u0065\u0074\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight,offsetX,offsetY);}let maxDotValue=910646^910518;console['\u006C\u006F\u0067']("\u0070\u0072\u0069\u006E\u0074\u0065\u0072\u0053\u006E",printerSn);if(bleManage['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](printerSn)){maxDotValue=967093^967669;}else if(bleManage['\u0067\u0065\u0074\u004D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065\u0054\u0079\u0070\u0065'](printerSn)){maxDotValue=801837^802413;}else{}console['\u006C\u006F\u0067']("\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065\u503C",maxDotValue);if(imageHeight>maxDotValue){imageHeight=maxDotValue;arr=this['\u0073\u0061\u0066\u0065\u0044\u0061\u0074\u0061'](arr,maxDotValue);}that['\u0077\u0069\u0064\u0074\u0068']=imageWidth;that['\u0068\u0065\u0069\u0067\u0068\u0074']=imageHeight;var _0xa21gg=(133470^133465)+(657368^657360);let pData=this['\u0070\u0072\u0069\u006E\u0074\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight);_0xa21gg=763010^763013;return pData;},rotateData(arr2,imageWidth,imageHeight,rotate){if(rotate==(131061^130991)){var _0xbf_0x8ad=(363999^363995)+(618917^618916);let bt=new Array(imageWidth);_0xbf_0x8ad=(259387^259385)+(896182^896176);for(let i=861459^861459;i<imageWidth;i++){let row=new Array(imageHeight);for(let j=479621^479621;j<imageHeight;j++){row[j]=arr2[imageHeight-j-(291616^291617)][i];}bt[i]=row;}return bt;}else if(rotate==(971452^971272)){let bt=new Array(imageHeight);for(let i=305436^305436;i<imageHeight;i++){let row=new Array(imageWidth);for(let j=140472^140472;j<imageWidth;j++){row[j]=arr2[imageHeight-i-(200026^200027)][imageWidth-j-(195183^195182)];}bt[i]=row;}return bt;}else if(rotate==(498008^497750)||rotate==-(886564^886654)){let bt=new Array(imageWidth);for(let i=197482^197482;i<imageWidth;i++){var _0x1c_0xaa2;let row=new Array(imageHeight);_0x1c_0xaa2='\u0062\u0070\u0065\u0065\u0070\u006C';for(let j=150348^150348;j<imageHeight;j++){row[j]=arr2[j][imageWidth-i-(603046^603047)];}bt[i]=row;}return bt;}else{return arr2;}},flipData(arr2,flipTYPE){var _0x7aaccg;let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x7aaccg="maikod".split("").reverse().join("");var _0x2_0xfga=(999420^999422)+(486705^486707);let imageWidth=arr2[725098^725098]['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x2_0xfga=805986^805985;if(flipTYPE==(180178^180179)){let bt=new Array(imageHeight);for(let i=783812^783812;i<imageHeight;i++){var _0xd8f=(926175^926169)+(347388^347389);let row=new Array(imageWidth);_0xd8f=(202941^202937)+(235451^235452);for(let j=183871^183871;j<imageWidth;j++){row[j]=arr2[i][imageWidth-j-(299184^299185)];}bt[i]=row;}return bt;}else if(flipTYPE==(216053^216055)){var _0x3e_0xdef;let bt=new Array(imageHeight);_0x3e_0xdef=(809539^809536)+(414290^414299);for(let i=587635^587635;i<imageHeight;i++){var _0xeb2eff=(323072^323075)+(471320^471324);let row=new Array(imageWidth);_0xeb2eff=697901^697896;for(let j=333670^333670;j<imageWidth;j++){row[j]=arr2[imageHeight-i-(346266^346267)][j];}bt[i]=row;}return bt;}else{return arr2;}},safeData(arr2,maxDotValue){var _0xbe7d=(236760^236764)+(560546^560554);let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];_0xbe7d=(734762^734760)+(813777^813776);if(imageHeight<=maxDotValue){return arr2;}let bt=new Array(maxDotValue);var _0xc5a38b=(441315^441323)+(643510^643506);let offsetY=Math['\u0066\u006C\u006F\u006F\u0072']((imageHeight-maxDotValue)/(404382^404380));_0xc5a38b=(370785^370785)+(357422^357417);for(let i=408048^408048;i<maxDotValue;i++){bt[i]=arr2[i+offsetY];}return bt;},clamp(r,g,b,a){let threshold=662827^662919;if(a>(254882^254754)){var _0x8acf;let gray=r*0.3+g*0.59+b*0.11;_0x8acf=(255188^255196)+(190406^190405);if(gray<threshold){return 438104^438105;}else{return 168395^168395;}}else{return 649895^649895;}},offsetData(arr2,imageWidth,imageHeight,offsetX,offsetY){var _0xf5688f;let bt=new Array(imageHeight);_0xf5688f=(906129^906132)+(228845^228840);let lastHeightIndex=imageHeight-(748729^748728);var _0xbf5b9c;let lastWidthIndex=imageWidth-(788408^788409);_0xbf5b9c=(733797^733792)+(648322^648322);for(let i=253816^253816;i<imageHeight;i++){let offsetI=i+offsetX;if(offsetI<(594675^594675)||offsetI>lastHeightIndex){var _0xe73g=(532495^532488)+(875795^875792);let row=new Array(imageWidth)['\u0066\u0069\u006C\u006C'](134101^134101);_0xe73g=(230560^230566)+(945968^945976);bt[i]=row;}else{var _0xdc_0x1cf=(499861^499856)+(223071^223063);let row=new Array(imageWidth);_0xdc_0x1cf='\u0064\u0066\u0069\u006D\u0062\u006E';for(let j=489826^489826;j<imageWidth;j++){let offsetJ=j-offsetY;if(offsetJ<(226503^226503)||offsetJ>lastWidthIndex){row[j]=944329^944329;}else{row[j]=arr2[offsetI][offsetJ];}}bt[i]=row;}}return bt;},printData(arr2){var _0xf6402d;const that=this;_0xf6402d=(688318^688315)+(141408^141412);var _0xa6394d;let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];_0xa6394d=653945^653948;let imageWidth=arr2[469455^469455]['\u006C\u0065\u006E\u0067\u0074\u0068'];console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0048\u0065\u0069\u0067\u0068\u0074",imageHeight);console['\u006C\u006F\u0067']("htdiWegami".split("").reverse().join(""),imageWidth);var _0x0cd=(837672^837674)+(453480^453482);let needUpdateTop=502775^502774;_0x0cd=783838^783833;var _0x27d5b=(653739^653742)+(678087^678080);let cx=imageHeight/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x27d5b=(608415^608414)+(620300^620303);console['\u006C\u006F\u0067']("\u0063\u0078",cx);that['\u0062\u0074']=new Array(cx*imageWidth)['\u0066\u0069\u006C\u006C'](354350^354350);for(let i=942904^942904;i<imageWidth;i++){for(let j=388231^388231;j<imageHeight;j++){let value=arr2[j][i];if(value==(619958^619959)){if(needUpdateTop==(820836^820837)){that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']=i;needUpdateTop=216266^216266;}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=i;}let index=cx*i+Math['\u0066\u006C\u006F\u006F\u0072'](j/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);let shift=j%that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];let btValue=that['\u0062\u0074'][index];btValue=btValue|value<<shift;that['\u0062\u0074'][index]=btValue;}}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']+(274405^274404);if(that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']>imageWidth){that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=imageWidth;}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=imageWidth-that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'];return that['\u0062\u0074'];},getBytes(nIndex){var _0x75cf=(998082^998085)+(437727^437720);const that=this;_0x75cf=(920407^920414)+(123057^123064);var _0x7c_0xe32=(957278^957273)+(738054^738054);let nCnt=Math['\u006D\u0069\u006E'](that['\u0077\u0069\u0064\u0074\u0068']-nIndex,344639^344638);_0x7c_0xe32=(247997^247996)+(323894^323888);if(nCnt<=(922204^922204)){return new Uint8Array();}let cx=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u0068\u0065\u0069\u0067\u0068\u0074']+(797371^797372))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);let b=new Uint8Array(cx*nCnt);for(var i=271543^271543;i<b['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){b[i]=that['\u0062\u0074'][nIndex*cx+i];}return b;},getBytesOne(nIndex,nCntNum){const that=this;let nCnt=Math['\u006D\u0069\u006E'](that['\u0077\u0069\u0064\u0074\u0068']-nIndex,nCntNum);if(nCnt<=(300419^300419)){return new Uint8Array();}let cx=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u0068\u0065\u0069\u0067\u0068\u0074']+(702856^702863))/that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);let b=new Uint8Array(cx*nCnt);for(var i=283035^283035;i<b['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){b[i]=that['\u0062\u0074'][nIndex*cx+i];}return b;},getBytesAll(){const that=this;return that['\u0062\u0074'];},getMarginTop(){var _0x4f6a=(935593^935592)+(891703^891710);const that=this;_0x4f6a=(976607^976606)+(548651^548654);return that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070'];},getMarginBottom(){const that=this;return that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'];},getWidth(){return this['\u0068\u0065\u0069\u0067\u0068\u0074']/this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},getHeight(){return this['\u0077\u0069\u0064\u0074\u0068']/this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},getClean(){var _0xf1b=(974455^974463)+(616012^616013);const that=this;_0xf1b='\u006E\u006F\u0066\u006A\u0062\u0065';that['\u0062\u0074']=[];that['\u0077\u0069\u0064\u0074\u0068']=656012^656012;that['\u0068\u0065\u0069\u0067\u0068\u0074']=881351^881351;that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']=139689^139689;that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=208609^208609;}};