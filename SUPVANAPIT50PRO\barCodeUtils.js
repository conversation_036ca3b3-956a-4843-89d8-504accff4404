import JsBarcode from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u006C\u0073\u002D\u0062\u0061\u0072\u0063\u006F\u0064\u0065\u002F\u006C\u0069\u0062\u0073\u002F\u004A\u0073\u0042\u0061\u0072\u0063\u006F\u0064\u0065\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{"objectData":{'\u0077\u0069\u0064\u0074\u0068':'',"height":'','\u0063\u0061\u006E\u0076\u0061\u0073':null},async getBarcodeObjectData(barcodeObject,query){try{var _0x6d_0xe94=(897664^897673)+(374645^374641);const that=this;_0x6d_0xe94=(400614^400610)+(741228^741229);const canvas=await that['\u0067\u0065\u0074\u0045\u006C\u0065\u006D\u0065\u006E\u0074'](query);return await that['\u0067\u0065\u006E\u0065\u0072\u0061\u0074\u0065\u0043\u006F\u0064\u0065'](canvas,barcodeObject);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0031\u0038'],error);}},getElement(query){return new Promise((resolve,reject)=>{query['\u0073\u0065\u006C\u0065\u0063\u0074']("\u0023"+"\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u006C\u0073")['\u0066\u0069\u0065\u006C\u0064\u0073']({'\u006E\u006F\u0064\u0065':!![],"size":!![]})['\u0065\u0078\u0065\u0063'](res=>{let canvas=null;if(res[281224^281224]&&res[723693^723693]['\u006E\u006F\u0064\u0065']){canvas=res[996014^996014]['\u006E\u006F\u0064\u0065'];}if(canvas){resolve(canvas);}else{reject();}});});},async generateCode(canvas,barcodeObject){var _0xg23c=(160338^160341)+(692029^692027);const that=this;_0xg23c=(131357^131348)+(142775^142775);var _0x4da=(283374^283367)+(136184^136187);var defaultOptions={'\u0066\u006F\u0072\u006D\u0061\u0074':"\u0045\u0041\u004E\u0031\u0033",'\u0077\u0069\u0064\u0074\u0068':2,'\u0068\u0065\u0069\u0067\u0068\u0074':56,"displayValue":!![],"text":"\u0036\u0039\u0030\u0032\u0038\u0039\u0030\u0038\u0038\u0034\u0039\u0031\u0030","textAlign":"center",'\u0074\u0065\u0078\u0074\u0050\u006F\u0073\u0069\u0074\u0069\u006F\u006E':"bottom",'\u0074\u0065\u0078\u0074\u004D\u0061\u0072\u0067\u0069\u006E':0,'\u0066\u006F\u006E\u0074\u0053\u0069\u007A\u0065':32,"fontColor":"#000000",'\u0062\u0061\u0063\u006B\u0067\u0072\u006F\u0075\u006E\u0064':"\u0074\u0072\u0061\u006E\u0073\u0070\u0061\u0072\u0065\u006E\u0074",'\u006D\u0061\u0072\u0067\u0069\u006E':0,"marginTop":0,"marginBottom":0,'\u006D\u0061\u0072\u0067\u0069\u006E\u004C\u0065\u0066\u0074':0,"marginRight":0};_0x4da=(917927^917925)+(958626^958630);this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']='';defaultOptions['\u0074\u0065\u0078\u0074']=barcodeObject['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'];let _0xd4da;let res=wx['\u0067\u0065\u0074\u0053\u0079\u0073\u0074\u0065\u006D\u0049\u006E\u0066\u006F\u0053\u0079\u006E\u0063']();_0xd4da='\u0064\u006E\u0064\u006F\u0063\u006E';let ratio=Math['\u0066\u006C\u006F\u006F\u0072'](barcodeObject['\u0057\u0069\u0064\u0074\u0068']*(434708^434716)/(129885^129846));if(ratio<(809355^809353)){ratio=451358^451356;}this['\u0072\u006F\u0074\u0061\u0074\u0065\u0053\u0063\u0061\u006C']=ratio;defaultOptions['\u0077\u0069\u0064\u0074\u0068']=ratio*res['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'];defaultOptions['\u0068\u0065\u0069\u0067\u0068\u0074']=(barcodeObject['\u0048\u0065\u0069\u0067\u0068\u0074']-(681858^681859))*(267904^267912)*res['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'];defaultOptions['\u0066\u006F\u006E\u0074\u0053\u0069\u007A\u0065']=(barcodeObject['\u0048\u0065\u0069\u0067\u0068\u0074']-(692015^692014))*(227469^227461)*res['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'];console['\u006C\u006F\u0067']("snoitpOtluafed".split("").reverse().join(""),defaultOptions);let jsBarcode=JsBarcode(canvas,barcodeObject['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'],defaultOptions);var _0x84f7aa=(255019^255011)+(429093^429088);let canvasWidth=canvas['\u0077\u0069\u0064\u0074\u0068']/res['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'];_0x84f7aa=(852396^852399)+(667861^667858);let canvasHeight=canvas['\u0068\u0065\u0069\u0067\u0068\u0074']/res['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'];console['\u006C\u006F\u0067']("\u0063\u0061\u006E\u0076\u0061\u0073\u002E\u0077\u0069\u0064\u0074\u0068",canvas['\u0077\u0069\u0064\u0074\u0068']);console['\u006C\u006F\u0067']("thgieh.savnac".split("").reverse().join(""),canvas['\u0068\u0065\u0069\u0067\u0068\u0074']);that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0077\u0069\u0064\u0074\u0068']=canvasWidth;that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0068\u0065\u0069\u0067\u0068\u0074']=canvasHeight;that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0063\u0061\u006E\u0076\u0061\u0073']=canvas;console['\u006C\u006F\u0067']("ataDtcejbo".split("").reverse().join(""),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);return that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061'];}};