import bleManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";export default{"bt":[],"width":0,'\u0068\u0065\u0069\u0067\u0068\u0074':0,'\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070':0,'\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D':0,'\u0070\u0072\u0069\u006E\u0074\u0065\u0072\u0053\u006E':0,"dpiValue":12,getAllBytes(objectData){console['\u006C\u006F\u0067']("\u0031\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061",objectData);var _0x25c=(613451^613448)+(254998^254999);const that=this;_0x25c=(531487^531482)+(263221^263228);let imageRgbaData=objectData['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'];var _0xf_0xb32=(142880^142889)+(537651^537655);let imageWidth=objectData['\u0057\u0069\u0064\u0074\u0068'];_0xf_0xb32='\u0065\u0070\u0063\u006D\u0070\u0064';var _0x39d=(602614^602610)+(594260^594256);let imageHeight=objectData['\u0048\u0065\u0069\u0067\u0068\u0074'];_0x39d=310963^310965;let rotate=objectData['\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065'];let offsetX=objectData['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D'];let offsetY=objectData['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D'];var _0xe7a6b=(597660^597652)+(675146^675148);let flipType=objectData['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'];_0xe7a6b=462344^462336;let printerSn=objectData['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleManage['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("eulaVipd.taht4".split("").reverse().join(""),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);if(!imageWidth){imageWidth=414077^414076;}if(!imageHeight){imageHeight=829673^829672;}if(!rotate){rotate=600433^600432;}if(!offsetX){offsetX=569890^569890;}if(!offsetY){offsetY=550352^550352;}if(!flipType){flipType=false;}if(!printerSn){printerSn='';}var _0xa_0xaae=(366739^366737)+(887345^887352);let arr=new Array(imageHeight);_0xa_0xaae=677995^677997;for(let i=605622^605622;i<imageHeight;i++){var _0x2b62c;let rowArr=new Array(imageWidth);_0x2b62c=(456290^456292)+(460346^460351);for(let j=173603^173603;j<imageWidth;j++){var _0xd2d11b;let index=(i*imageWidth+j)*(279383^279379);_0xd2d11b=807072^807073;var _0x61cbff=(296228^296226)+(749518^749510);let r=imageRgbaData[index];_0x61cbff=(985808^985810)+(120693^120701);let g=imageRgbaData[index+(124259^124258)];var _0xe_0xa16;let b=imageRgbaData[index+(411530^411528)];_0xe_0xa16=(285054^285053)+(753634^753632);var _0x117f=(965558^965566)+(983919^983913);let a=imageRgbaData[index+(872698^872697)];_0x117f="gjgfmj".split("").reverse().join("");var _0x6d441d;let value=this['\u0063\u006C\u0061\u006D\u0070'](r,g,b,a);_0x6d441d=(372422^372431)+(914642^914643);rowArr[j]=value;}arr[i]=rowArr;}if(rotate!=(956757^956757)){arr=this['\u0072\u006F\u0074\u0061\u0074\u0065\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight,rotate);if(rotate==(976541^976583)||rotate==(734569^734311)||rotate==-(706536^706482)){console['\u006C\u006F\u0067']("\u65CB\u8F6C",rotate);let temp=imageWidth;imageWidth=imageHeight;imageHeight=temp;}}console['\u006C\u006F\u0067']("\u7FFB\u8F6C\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003D\u003E",flipType);if(flipType>(739370^739370)){arr=this['\u0066\u006C\u0069\u0070\u0044\u0061\u0074\u0061'](arr,855133^855132);arr=this['\u0066\u006C\u0069\u0070\u0044\u0061\u0074\u0061'](arr,368319^368317);}if(offsetX!=(794193^794193)||offsetY!=(600051^600051)){arr=this['\u006F\u0066\u0066\u0073\u0065\u0074\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight,offsetX,offsetY);}let maxDotValue=533181^533309;console['\u006C\u006F\u0067']("nSretnirp".split("").reverse().join(""),printerSn);if(bleManage['\u0074\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](printerSn)){maxDotValue=488643^489091;}else if(bleManage['\u0067\u0065\u0074\u004D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065\u0054\u0079\u0070\u0065'](printerSn)){maxDotValue=871453^872029;}else{}console['\u006C\u006F\u0067']("\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065\u503C",maxDotValue);if(imageHeight>maxDotValue){imageHeight=maxDotValue;arr=this['\u0073\u0061\u0066\u0065\u0044\u0061\u0074\u0061'](arr,maxDotValue);}that['\u0077\u0069\u0064\u0074\u0068']=imageWidth;that['\u0068\u0065\u0069\u0067\u0068\u0074']=imageHeight;let pData=this['\u0070\u0072\u0069\u006E\u0074\u0044\u0061\u0074\u0061'](arr,imageWidth,imageHeight);return pData;},rotateData(arr2,imageWidth,imageHeight,rotate){if(rotate==(534166^534220)){var _0xc_0xg47=(396682^396684)+(866894^866886);let bt=new Array(imageWidth);_0xc_0xg47=(706351^706349)+(248132^248129);for(let i=813450^813450;i<imageWidth;i++){var _0x4a5bf;let row=new Array(imageHeight);_0x4a5bf=(128548^128549)+(161232^161241);for(let j=233307^233307;j<imageHeight;j++){row[j]=arr2[imageHeight-j-(701680^701681)][i];}bt[i]=row;}return bt;}else if(rotate==(878371^878487)){let bt=new Array(imageHeight);for(let i=995011^995011;i<imageHeight;i++){var _0xdfb7ed=(873313^873319)+(264724^264720);let row=new Array(imageWidth);_0xdfb7ed=(203190^203185)+(310811^310809);for(let j=577426^577426;j<imageWidth;j++){row[j]=arr2[imageHeight-i-(781227^781226)][imageWidth-j-(686844^686845)];}bt[i]=row;}return bt;}else if(rotate==(148421^148171)||rotate==-(692314^692224)){var _0x8fdc5d;let bt=new Array(imageWidth);_0x8fdc5d=277510^277511;for(let i=401924^401924;i<imageWidth;i++){let row=new Array(imageHeight);for(let j=179041^179041;j<imageHeight;j++){row[j]=arr2[j][imageWidth-i-(958023^958022)];}bt[i]=row;}return bt;}else{return arr2;}},flipData(arr2,flipTYPE){var _0xbe5c=(557211^557211)+(211363^211370);let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];_0xbe5c='\u006A\u006C\u0067\u006E\u0066\u0070';var _0x9d_0xg1d;let imageWidth=arr2[647412^647412]['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x9d_0xg1d=559870^559866;if(flipTYPE==(303116^303117)){let bt=new Array(imageHeight);for(let i=763444^763444;i<imageHeight;i++){var _0xb_0x62e;let row=new Array(imageWidth);_0xb_0x62e=666737^666737;for(let j=435351^435351;j<imageWidth;j++){row[j]=arr2[i][imageWidth-j-(867756^867757)];}bt[i]=row;}return bt;}else if(flipTYPE==(565108^565110)){var _0x_0x9e4=(322594^322594)+(268017^268018);let bt=new Array(imageHeight);_0x_0x9e4="agcbge".split("").reverse().join("");for(let i=111680^111680;i<imageHeight;i++){var _0xea0eee;let row=new Array(imageWidth);_0xea0eee=(943924^943920)+(787239^787232);for(let j=932557^932557;j<imageWidth;j++){row[j]=arr2[imageHeight-i-(158038^158039)][j];}bt[i]=row;}return bt;}else{return arr2;}},safeData(arr2,maxDotValue){var _0xb7fbca;let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];_0xb7fbca="gdiheh".split("").reverse().join("");if(imageHeight<=maxDotValue){return arr2;}var _0x2g8b5d=(861184^861191)+(979991^979985);let bt=new Array(maxDotValue);_0x2g8b5d=(778849^778849)+(191694^191690);var _0x07c0ec=(157330^157338)+(792952^792958);let offsetY=Math['\u0066\u006C\u006F\u006F\u0072']((imageHeight-maxDotValue)/(228133^228135));_0x07c0ec='\u006F\u0061\u006D\u0069\u006F\u006A';for(let i=399988^399988;i<maxDotValue;i++){bt[i]=arr2[i+offsetY];}return bt;},clamp(r,g,b,a){let threshold=662133^662233;if(a>(668466^668594)){var _0x02df=(570918^570927)+(452477^452476);let gray=r*0.3+g*0.59+b*0.11;_0x02df=(313934^313932)+(102239^102234);if(gray<threshold){return 676440^676441;}else{return 426155^426155;}}else{return 987295^987295;}},offsetData(arr2,imageWidth,imageHeight,offsetX,offsetY){var _0x76ga=(199020^199023)+(683105^683107);let bt=new Array(imageHeight);_0x76ga=978650^978648;var _0xca36b=(210539^210539)+(174278^174276);let lastHeightIndex=imageHeight-(374851^374850);_0xca36b=(435760^435764)+(953155^953156);let lastWidthIndex=imageWidth-(373625^373624);for(let i=156514^156514;i<imageHeight;i++){let offsetI=i+offsetX;if(offsetI<(401475^401475)||offsetI>lastHeightIndex){let row=new Array(imageWidth)['\u0066\u0069\u006C\u006C'](575097^575097);bt[i]=row;}else{var _0xa9a=(841139^841139)+(452037^452039);let row=new Array(imageWidth);_0xa9a="heneha".split("").reverse().join("");for(let j=932847^932847;j<imageWidth;j++){let offsetJ=j-offsetY;if(offsetJ<(592781^592781)||offsetJ>lastWidthIndex){row[j]=335755^335755;}else{row[j]=arr2[offsetI][offsetJ];}}bt[i]=row;}}return bt;},printData(arr2){const that=this;let imageHeight=arr2['\u006C\u0065\u006E\u0067\u0074\u0068'];var _0x2_0xdb2=(399692^399684)+(458882^458887);let imageWidth=arr2[707669^707669]['\u006C\u0065\u006E\u0067\u0074\u0068'];_0x2_0xdb2='\u0065\u0065\u0070\u0068\u006F\u006B';console['\u006C\u006F\u0067']("thgieHegami".split("").reverse().join(""),imageHeight);console['\u006C\u006F\u0067']("htdiWegami".split("").reverse().join(""),imageWidth);var _0x254ca;let needUpdateTop=509754^509755;_0x254ca=(550579^550578)+(527992^527995);let cx=imageHeight/(812523^812515);console['\u006C\u006F\u0067']("\u0063\u0078",cx);that['\u0062\u0074']=new Array(cx*imageWidth)['\u0066\u0069\u006C\u006C'](472119^472119);for(let i=697362^697362;i<imageWidth;i++){for(let j=514398^514398;j<imageHeight;j++){let value=arr2[j][i];if(value==(429647^429646)){if(needUpdateTop==(631257^631256)){that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']=i;needUpdateTop=606037^606037;}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=i;}let index=cx*i+Math['\u0066\u006C\u006F\u006F\u0072'](j/(752686^752678));var _0xfec;let shift=j%(370480^370488);_0xfec=(713904^713907)+(654114^654119);var _0xd5df2f;let btValue=that['\u0062\u0074'][index];_0xd5df2f="hkhlbo".split("").reverse().join("");btValue=btValue|value<<shift;that['\u0062\u0074'][index]=btValue;}}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']+(862484^862485);if(that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']>imageWidth){that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=imageWidth;}that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=imageWidth-that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'];return that['\u0062\u0074'];},getBytes(nIndex){const that=this;let nCnt=Math['\u006D\u0069\u006E'](that['\u0077\u0069\u0064\u0074\u0068']-nIndex,170627^170626);if(nCnt<=(925383^925383)){return new Uint8Array();}var _0x4f2dfd=(718306^718311)+(689050^689049);let cx=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u0068\u0065\u0069\u0067\u0068\u0074']+(398610^398613))/(311586^311594));_0x4f2dfd=(789911^789911)+(330861^330859);let b=new Uint8Array(cx*nCnt);for(var i=783264^783264;i<b['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){b[i]=that['\u0062\u0074'][nIndex*cx+i];}return b;},getBytesOne(nIndex,nCntNum){const that=this;var _0xbb_0xgfg=(653165^653166)+(272950^272959);let nCnt=Math['\u006D\u0069\u006E'](that['\u0077\u0069\u0064\u0074\u0068']-nIndex,nCntNum);_0xbb_0xgfg=(764065^764070)+(527801^527802);if(nCnt<=(570699^570699)){return new Uint8Array();}let cx=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u0068\u0065\u0069\u0067\u0068\u0074']+(791238^791233))/(622809^622801));var _0xdf637e=(771904^771911)+(699404^699403);let b=new Uint8Array(cx*nCnt);_0xdf637e="eqjjbn".split("").reverse().join("");for(var i=931576^931576;i<b['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){b[i]=that['\u0062\u0074'][nIndex*cx+i];}return b;},getBytesAll(){var _0xef7ca;const that=this;_0xef7ca=462705^462705;return that['\u0062\u0074'];},getMarginTop(){var _0x891fd;const that=this;_0x891fd=(135747^135751)+(779390^779387);return that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070'];},getMarginBottom(){const that=this;return that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'];},getWidth(){return this['\u0068\u0065\u0069\u0067\u0068\u0074']/this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},getHeight(){return this['\u0077\u0069\u0064\u0074\u0068']/this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},getClean(){var _0x3_0x93a;const that=this;_0x3_0x93a=(475237^475232)+(239726^239725);that['\u0062\u0074']=[];that['\u0077\u0069\u0064\u0074\u0068']=603321^603321;that['\u0068\u0065\u0069\u0067\u0068\u0074']=913145^913145;that['\u006D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']=848316^848316;that['\u006D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D']=967403^967403;}};