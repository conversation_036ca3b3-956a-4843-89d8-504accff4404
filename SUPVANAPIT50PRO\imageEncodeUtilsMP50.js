import printingControl from"\u002E\u002F\u0050\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0043\u006F\u006E\u0074\u0072\u006F\u006C\u002E\u006A\u0073";import pr from"\u002E\u002F\u0050\u0041\u0047\u0045\u005F\u0052\u0045\u0047\u005F\u0042\u0049\u0054\u0053\u002E\u006A\u0073";import lzma from"\u002E\u002F\u006C\u007A\u006D\u0061\u0055\u0074\u0069\u006C\u0073\u002E\u006A\u0073";import imageDataUtils from"\u002E\u002F\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030";import supVanPrintUtilsMP50 from"\u002E\u002F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import bleToothManage from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065";import bleTool from"\u002E\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C";import constants from"\u002E\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{'\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068':50,"matHeight":30,'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':12,"imageRgbaData":[],"encodeList":[],'\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':0,'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C':[],'\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074':0,'\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065':false,"bufferPackageList":[],'\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061':null,"printType":'',"callback":null,'\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065':384,async initEncodeData(objectData,imageRgbaData){try{console['\u006C\u006F\u0067']("\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030");var _0x1fg=(856463^856459)+(410555^410554);const that=this;_0x1fg=193122^193124;that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();console['\u006C\u006F\u0067']("\u0074\u0068\u0061\u0074\u002E\u0064\u0070\u0069\u0076\u0061\u006C\u0075\u0065",that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);if(that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']==(664907^664899)){that['\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065']=398974^399358;}else{that['\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065']=951804^952252;}that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']=objectData;that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061']=imageRgbaData;that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065']=objectData['\u0069\u0073\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E']?979885^979884:702546^702546;await that['\u0069\u006E\u0069\u0074\u0069\u0061\u006C\u0069\u007A\u0065\u0044\u0061\u0074\u0061']();await that['\u0069\u006E\u0069\u0074\u004C\u005A\u004D\u0041\u0044\u0061\u0074\u0061']();await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074']();}catch(error){throw error;}},initializeData(){var _0x9c9a3f=(305957^305959)+(448740^448737);const that=this;_0x9c9a3f=(463582^463580)+(368561^368569);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];printingControl['\u0063\u006C\u0065\u0061\u006E\u0050\u0063']();imageDataUtils['\u0067\u0065\u0074\u0043\u006C\u0065\u0061\u006E']();pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']==(284032^284032)){that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']=228715^228714;}var _0xdd_0x58d;let rotateAngle=(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0052\u006F\u0074\u0061\u0074\u0065']-(626930^626928))*(269309^269223);_0xdd_0x58d='\u0068\u0070\u0062\u006F\u0066\u0066';let object={'\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061':that['\u0069\u006D\u0061\u0067\u0065\u0052\u0067\u0062\u0061\u0044\u0061\u0074\u0061'],"Width":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0048\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0072\u006F\u0074\u0061\u0074\u0065\u0041\u006E\u0067\u006C\u0065':rotateAngle,"HorizontalNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D']*(479451^479455),"VerticalNum":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D']*(536523^536527),'\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065':that['\u004F\u0076\u0065\u0072\u0074\u0075\u0072\u006E\u0054\u0079\u0070\u0065'],"DeviceSn":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E']};console['\u006C\u006F\u0067']("\u006F\u0062\u006A\u0065\u0063\u0074",object);imageDataUtils['\u0067\u0065\u0074\u0041\u006C\u006C\u0042\u0079\u0074\u0065\u0073'](object);that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']=imageDataUtils['\u0067\u0065\u0074\u0057\u0069\u0064\u0074\u0068']();that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']=imageDataUtils['\u0067\u0065\u0074\u0048\u0065\u0069\u0067\u0068\u0074']();printingControl['\u0043\u006F\u006C\u0075\u006D\u006E\u004C\u0065\u0066\u0074']=that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];},async initLZMAData(){try{var _0xc24bae=(825568^825576)+(292030^292024);const that=this;_0xc24bae=(763697^763699)+(503719^503715);let bufferTransferCount=659716^659716;var _0x_0x63c;let num=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0043\u006F\u0070\u0069\u0065\u0073'];_0x_0x63c='\u006B\u006A\u0069\u0067\u0070\u0062';var _0xe6bab=(285227^285224)+(328438^328437);let _bufLength=689153^693249;_0xe6bab=201533^201528;var _0x4ge4be;let _maxBufLength=972716^980908;_0x4ge4be='\u006A\u0065\u0061\u006B\u0067\u0069';var _0xd88df=(748224^748227)+(532008^532008);let countBuff=new Array();_0xd88df='\u0064\u0066\u0068\u0070\u006D\u0067';var _0x6579a;let isEndFlag=!![];_0x6579a=(102322^102325)+(783682^783684);let imgTotalCount=359000^359001;let marginleft;let marginright;if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(727938^727937)){marginleft=Math['\u006D\u0061\u0078'](imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070']()-(917228^917229),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*1.5);marginright=Math['\u006D\u0061\u0078'](imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'](),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*1.5);}else{marginleft=Math['\u006D\u0061\u0078'](imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0054\u006F\u0070'](),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);marginright=Math['\u006D\u0061\u0078'](imageDataUtils['\u0067\u0065\u0074\u004D\u0061\u0072\u0067\u0069\u006E\u0042\u006F\u0074\u0074\u006F\u006D'](),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);}console['\u006C\u006F\u0067']("\u8DDD\u8FB9\u5DE6".split("").reverse().join(""),marginleft);console['\u006C\u006F\u0067']("\u8DDD\u8FB9\u53F3".split("").reverse().join(""),marginright);console['\u006C\u006F\u0067']("thgieHtam".split("").reverse().join(""),that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']);let _nColumnTotalCnt=that['\u006D\u0061\u0074\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-marginleft-marginright;console['\u006C\u006F\u0067']("\u56FE\u7247\u5217\u6570",_nColumnTotalCnt);console['\u006C\u006F\u0067']("eulaVipd".split("").reverse().join(""),that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065",that['\u006D\u0061\u0078\u0044\u006F\u0074\u0056\u0061\u006C\u0075\u0065']);var _0x5776d=(668718^668718)+(390827^390831);let nBytePerLine=Math['\u0066\u006C\u006F\u006F\u0072']((that['\u006D\u0061\u0074\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(338193^338198))/(600037^600045));_0x5776d=(508867^508864)+(900474^900475);console['\u006C\u006F\u0067']("\u6BCF\u5217\u5B57\u8282\u6570",nBytePerLine);let nMax=Math['\u0066\u006C\u006F\u006F\u0072']((_bufLength-(925249^925271))/nBytePerLine);console['\u006C\u006F\u0067']("\u6570\u5217\u5927\u6700\u533A\u51B2\u7F13\u4E2A\u6BCF".split("").reverse().join(""),nMax);var _0xe0f=(903058^903060)+(636263^636261);let bufferCountImage=Math['\u0066\u006C\u006F\u006F\u0072']((_nColumnTotalCnt+nMax-(920811^920810))/nMax);_0xe0f=(850566^850564)+(952855^952851);console['\u006C\u006F\u0067']("egamItnuoCreffub \u91CF\u6570\u533A\u51B2\u7F13\u7247\u56FE\u4E2A\u6BCF".split("").reverse().join(""),bufferCountImage);var _0x8ef7ff=(993782^993776)+(625325^625321);let allBytes=imageDataUtils['\u0067\u0065\u0074\u0042\u0079\u0074\u0065\u0073\u0041\u006C\u006C']();_0x8ef7ff=(732039^732046)+(615706^615708);console['\u006C\u006F\u0067']("\u5EA6\u957Fsetyblla".split("").reverse().join(""),allBytes['\u006C\u0065\u006E\u0067\u0074\u0068']);for(var n=424877^424877;n<imgTotalCount;n++){that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']++;for(var i=843497^843497;i<bufferCountImage;i++){let _btBuf=new Uint8Array(_bufLength);pr['\u0063\u006C\u0065\u0061\u006E\u0050\u0061\u0067\u0065']();pr['\u0073\u0065\u0074\u0043\u0075\u0074'](989614^989614);pr['\u0073\u0065\u0074\u004E\u006F\u0064\u0075'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);pr['\u0073\u0065\u0074\u004D\u0061\u0074'](359591^359590);pr['\u0073\u0065\u0074\u0053\u0061\u0076\u0065\u0070\u0061\u0070\u0065\u0072'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']['\u0053\u0061\u0076\u0065\u0050\u0061\u0070\u0065\u0072']);pr['\u0073\u0065\u0074\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074'](that['\u0046\u0069\u0072\u0073\u0074\u0043\u0075\u0074']);if(i==(549379^549379)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0053\u0074'](699188^699189);}var _0x4efbaa;let bufferColumnCnt=334405^334405;_0x4efbaa=718361^718363;if(i==bufferCountImage-(678341^678340)){pr['\u0073\u0065\u0074\u0050\u0061\u0067\u0065\u0045\u006E\u0064'](389163^389162);if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']==bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()&&isEndFlag){pr['\u0073\u0065\u0074\u0050\u0072\u0074\u0045\u006E\u0064'](195153^195152);}bufferColumnCnt=_nColumnTotalCnt-nMax*i;}else{bufferColumnCnt=nMax;}var _0x76514a;let star=nMax*i*nBytePerLine+marginleft*nBytePerLine;_0x76514a=(914335^914330)+(928951^928951);var _0x8831db;let end=star+bufferColumnCnt*nBytePerLine;_0x8831db=(336109^336101)+(520914^520912);var _0x8c_0xeaf=(938506^938510)+(545845^545843);let b=allBytes['\u0073\u006C\u0069\u0063\u0065'](star,end);_0x8c_0xeaf=101323^101315;_btBuf[193905^193909]=bufferColumnCnt&(763564^763475);_btBuf[604399^604394]=bufferColumnCnt>>(187970^187978)&(762158^762321);for(var y=208906^208906;y<b['\u006C\u0065\u006E\u0067\u0074\u0068'];y++){_btBuf[(330161^330175)+y]=b[y];}let btdata=pr['\u0074\u006F\u0042\u0079\u0074\u0065\u0041\u0072\u0072\u0061\u0079'](350035^350037);for(var z=358824^358826;z<(641704^641708);z++){_btBuf[z]=btdata[z-(757702^757700)];}if(marginleft<(773870^773871)){marginleft=110887^110886;}if(marginleft>(799698^799514)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']){marginleft=(281153^281225)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];}if(marginright<(878609^878608)){marginright=783693^783692;}if(marginright>(895575^895647)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']){marginright=(793554^793370)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];}_btBuf[188042^188034]=marginleft&(621078^621289);_btBuf[146874^146867]=marginleft>>(317414^317422)&(180383^180320);_btBuf[855580^855574]=marginright&(972563^972780);_btBuf[234889^234882]=marginright>>(761912^761904)&(454836^454731);_btBuf[608032^608038]=nBytePerLine&(777262^777425);_btBuf[865826^865838]=840061^840061;_btBuf[554567^554570]=322825^322825;var _0x72944d=(239109^239111)+(676437^676438);let len=_btBuf[178086^178083];_0x72944d="qjloll".split("").reverse().join("");len<<=298788^298796;len+=_btBuf[139135^139131];len*=_btBuf[998307^998309];len+=938240^938254;let un=350149^350149;for(var j=245903^245901;j<(301691^301685);j++){un+=_btBuf[j];}var _0x8a639c=(998032^998035)+(703748^703747);let x=Math['\u0066\u006C\u006F\u006F\u0072'](len/(677083^677339));_0x8a639c="jfajnd".split("").reverse().join("");if(x>(660385^660385)){for(var k=408297^408297;k<x;k++){un+=_btBuf[(k+(829351^829350))*(370458^370202)-(846612^846613)];}}_btBuf[352134^352134]=un;_btBuf[598558^598559]=un>>(174300^174292);countBuff['\u0070\u0075\u0073\u0068'](_btBuf);console['\u006C\u006F\u0067']("\u0070\u0072",pr);}}while(bufferTransferCount<bufferCountImage*num&&countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']>(600294^600294)){let sendData=null;let bufferCount=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);var _0xdf25c=(163635^163636)+(602776^602769);let bufferCountOne=Math['\u006D\u0069\u006E'](bufferCountImage,countBuff['\u006C\u0065\u006E\u0067\u0074\u0068']);_0xdf25c="qediii".split("").reverse().join("");do{var _0x8dd6fe=(932190^932185)+(610792^610799);let bufferPackage=Array();_0x8dd6fe=(248351^248342)+(785446^785455);for(let a=639238^639238;a<bufferCount;a++){for(var b=765024^765024;b<_bufLength;b++){bufferPackage[b+a*_bufLength]=countBuff[a][b];}}try{this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=[];this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']=bufferPackage;sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](bufferPackage);}catch(error){console['\u006C\u006F\u0067']("\u538B\u7F29\u5931\u8D25");sendData=[];that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']=[];num=597887^597887;throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}bufferCount--;}while(sendData['\u004C\u0065\u006E\u0067\u0074\u0068']>_maxBufLength);bufferTransferCount=bufferTransferCount+bufferCount+(344000^344001);countBuff['\u0073\u0070\u006C\u0069\u0063\u0065'](219446^219446,bufferCountOne);if(sendData['\u006C\u0065\u006E\u0067\u0074\u0068']>(347540^347540)){that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}}}catch(error){throw error;}},async onDataProcessing(data){try{const that=this;let sendData=data;var _0xa95c;let type=false;_0xa95c='\u0065\u0065\u0067\u0061\u006A\u0066';for(var i=632848^632849;i<(215666^215670);i++){let dataIndex3=sendData[i*(413710^413838)+(238619^238616)];let dataIndex2=sendData[i*(934430^934558)+(290420^290422)];let dlen=dataIndex3<<(769544^769536)|dataIndex2;dlen=dlen+(695275^695279);let opcode=sendData[i*(928209^928081)+(758810^758812)];let dataIndex6=sendData[i*(548197^548325)+(480605^480600)];if(dlen>(230982^231110)&&(opcode==(284137^283986)||opcode==(512013^512011)||opcode==(483845^484034)||opcode==(505294^505289))&&dataIndex6==(586295^586397)){this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(388824^388696)]=this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074'][i*(966692^966820)]+(787563^787562);type=!![];console['\u006C\u006F\u0067']("\u8FDB\u5165\u9519\u8BEF\u003A\u0020","\u91CD\u65B0\u538B\u7F29");}}if(type){sendData=[];console['\u006C\u006F\u0067']("\u7F29\u538B\u6B21\u518D".split("").reverse().join(""));try{sendData=await lzma['\u0067\u0065\u0074\u004C\u007A\u006D\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](this['\u0062\u0075\u0066\u0066\u0065\u0072\u0050\u0061\u0063\u006B\u0061\u0067\u0065\u004C\u0069\u0073\u0074']);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0034'],error);}}that['\u0065\u006E\u0063\u006F\u0064\u0065\u004C\u0069\u0073\u0074']['\u0070\u0075\u0073\u0068'](sendData);that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0070\u0075\u0073\u0068'](sendData);}catch(error){throw error;}},doPrint(){var _0x16gfdd=(217532^217525)+(638319^638312);const that=this;_0x16gfdd=(750281^750287)+(672610^672608);if(!that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']){that['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=!![];if(that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u006C\u0065\u006E\u0067\u0074\u0068']!=(743758^743758)){supVanPrintUtilsMP50['\u0064\u006F\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0044\u0061\u0074\u0061']);}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0035'],"\u7A7A\u4E3A\u80FD\u4E0D\u636E\u6570\u6A21\u5B57\u5370\u6253".split("").reverse().join("")));}}else{supVanPrintUtilsMP50['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0050\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C'][307200^307200]);}},cleanImageDataListAll(){this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C\u0054\u006F\u0074\u0061\u006C\u0043\u006E\u0074']=826009^826009;this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u0072\u0073\u0074\u0054\u0079\u0070\u0065']=false;},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};