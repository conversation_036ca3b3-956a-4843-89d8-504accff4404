import bleManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import baseSupPrint from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u0061\u0073\u0065\u0053\u0075\u0070\u0056\u0061\u006E\u0050\u0072\u0069\u006E\u0074\u002E\u006A\u0073";import ptFlag from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0050\u0052\u0049\u004E\u0054\u0045\u0052\u005F\u0046\u004C\u0041\u0047\u002E\u006A\u0073";import bleToothManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{"CMD_BUF_FULL":0x10,'\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041':0x11,"CMD_CHECK_DEVICE":0x12,'\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054':0x13,"CMD_NEXTFRM_FIRMWARE_BULK":0xc6,"CMD_STOP_PRINT":0x14,'\u0043\u004D\u0044\u005F\u0052\u0045\u0054\u0055\u0052\u004E\u005F\u004D\u0041\u0054':0x30,'\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B':0x5c,'\u0043\u004D\u0044\u005F\u0052\u0045\u0041\u0044\u005F\u0046\u0049\u0052\u004D\u0057\u0041\u0052\u0045\u005F\u0052\u0045\u0056':0xc5,"CMD_TRANSFER":0xf0,'\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041':0x5d,"dataLength":500,"cnt":0,'\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073':"\u0069\u006E\u0069\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073","frameNumber":0,"currentCommand":0,"imageDataListAll":[],"imageDataList":[],"isStop":false,'\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061':[],'\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E':'',"paperType":1,'\u0067\u0061\u0070':3,'\u0073\u0070\u0065\u0065\u0064':30,"noInstallType":false,'\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D':0,'\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D':0,"prtStarNum":0,"printingStationNum":0,"printType":'','\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,'\u006D\u0061\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B':null,"printMessage":{"msgType":"\u0030","printNum":0},"consumableInformationData":{"paperDirectionSize":'',"printHeadDirectionSize":'','\u0067\u0061\u0070':''},notifyData(notifyMessage){this['\u0068\u0061\u006E\u0064\u006C\u0065\u004E\u006F\u0074\u0069\u0066\u0079'](notifyMessage);},doSupVanPrint(supvanImageData,nObjectData){var _0xcd6e6c=(675697^675699)+(458866^458871);const that=this;_0xcd6e6c=(642573^642571)+(645929^645931);console['\u006C\u006F\u0067']("\u0073\u0075\u0070\u0076\u0061\u006E\u0070\u0072\u0069\u006E\u0074\u0075\u0074\u0069\u006C\u0073\u0054\u0035\u0030\u0070\u0072\u006F");var _0x5cbb=(166827^166819)+(871323^871322);let objectData=nObjectData;_0x5cbb=584720^584723;that['\u0069\u0073\u0053\u0074\u006F\u0070']=false;that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];that['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=157336^157336;ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074']=821511^821511;that['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']=254532^254532;that['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=691964^691924;that['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=736300^736260;that['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=619527^619567;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']=supvanImageData;that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074\u0041\u006C\u006C']['\u0073\u0068\u0069\u0066\u0074']();that['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0069\u006E\u0069\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";that['\u0064\u0065\u0076\u0069\u0063\u0065\u0053\u006E']=objectData['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'];that['\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']=objectData['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065'];that['\u0067\u0061\u0070']=objectData['\u0047\u0061\u0070'];that['\u0073\u0070\u0065\u0065\u0064']=objectData['\u0053\u0070\u0065\u0065\u0064'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']=[470729^470777,392817^392817,428046^428046,703771^703771,736696^736696,665915^665915,169443^169443,184666^184666,320736^320736,260236^260236,207186^207186,491153^491153,830729^830729,322886^322886,482175^482175,151255^151255,651987^651946,912439^912438,838366^838367,857127^857212,752489^752514,394335^394242,149520^149643,430243^430096,962358^962310,357707^357694,116837^116836,126665^126715,362179^362225,905766^905767,133711^133708,183632^183632,271109^271333,473442^473443,783462^783462,217944^217944,112477^112588,883997^883992,732861^732704,612099^612096,902902^902881,693048^693040,992783^992798,406332^406327,546937^546889,481688^481697,545229^545229,687978^687978,808336^808215,309375^309411,148174^148057,468567^468634,112029^112028,182759^182535,736713^736598,784480^784416,439557^439696,546314^546382,713040^712989,576453^576320,212074^212102,395543^395696,299600^299677,716109^716109,706061^706061,963620^963620,441366^441366,200153^200153,317198^317198,956045^956047,521772^521772,456907^456907,779793^779793,588788^588788,883415^883415,888247^888247,567917^567917,515994^515994,496064^496064,542738^542738];that['\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074'](objectData);},startPrint(objectData){var _0xfe82de=(266220^266216)+(935223^935223);const that=this;_0xfe82de=(383856^383860)+(967765^967766);if(that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']['\u006C\u0065\u006E\u0067\u0074\u0068']==(409631^409631)){this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();}else{that['\u0073\u0065\u006E\u0064\u0052\u0066\u0069\u0064\u0044\u0061\u0074\u0061'](objectData);}},sendRfidData(nObjectData){const that=this;let objectData=nObjectData;that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][769014^769004]=that['\u0070\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][375474^375468]=that['\u0067\u0061\u0070'];that['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=that['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041'];if(bleManage['\u0067\u0065\u0074\u0050\u0068\u006F\u006E\u0065\u004D\u006F\u0064\u0065\u006C']()){baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](that['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],720845^720845);}else{if(that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][322107^322081]==(927214^927212)){that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][831716^831742]=133677^133672;}if(objectData['\u0052\u006F\u0074\u0061\u0074\u0065']==(610578^610578)||objectData['\u0052\u006F\u0074\u0061\u0074\u0065']==(306495^306494)){that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][993483^993488]=objectData['\u0057\u0069\u0064\u0074\u0068'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][545617^545613]=objectData['\u0048\u0065\u0069\u0067\u0068\u0074'];}else{that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][560031^560004]=objectData['\u0048\u0065\u0069\u0067\u0068\u0074'];that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][530104^530084]=objectData['\u0057\u0069\u0064\u0074\u0068'];}that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061'][321539^321564]=493169^493169;console['\u006C\u006F\u0067']("\u4E0B\u53D1\u0052\u0046\u0049\u0044\u6570\u636E",JSON['\u0073\u0074\u0072\u0069\u006E\u0067\u0069\u0066\u0079'](that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']));baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u0052\u0061\u006E\u0064\u006F\u006D'](that['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041'],that['\u0072\u0066\u0069\u0064\u0044\u0061\u0074\u0061']);}},handleNotify(notifyMessage){switch(this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']){case this['\u0043\u004D\u0044\u005F\u0052\u0045\u0054\u0055\u0052\u004E\u005F\u004D\u0041\u0054']:this['\u0063\u006F\u006E\u0073\u0075\u006D\u0061\u0062\u006C\u0065\u0049\u006E\u0066\u006F\u0072\u006D\u0061\u0074\u0069\u006F\u006E\u0044\u0061\u0074\u0061']['\u0070\u0072\u0069\u006E\u0074\u0048\u0065\u0061\u0064\u0044\u0069\u0072\u0065\u0063\u0074\u0069\u006F\u006E\u0053\u0069\u007A\u0065']=notifyMessage[844420^844460];this['\u0063\u006F\u006E\u0073\u0075\u006D\u0061\u0062\u006C\u0065\u0049\u006E\u0066\u006F\u0072\u006D\u0061\u0074\u0069\u006F\u006E\u0044\u0061\u0074\u0061']['\u0070\u0061\u0070\u0065\u0072\u0044\u0069\u0072\u0065\u0063\u0074\u0069\u006F\u006E\u0053\u0069\u007A\u0065']=notifyMessage[786112^786153];this['\u0063\u006F\u006E\u0073\u0075\u006D\u0061\u0062\u006C\u0065\u0049\u006E\u0066\u006F\u0072\u006D\u0061\u0074\u0069\u006F\u006E\u0044\u0061\u0074\u0061']['\u0067\u0061\u0070']=notifyMessage[274740^274718];this['\u006D\u0061\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](this['\u0063\u006F\u006E\u0073\u0075\u006D\u0061\u0062\u006C\u0065\u0049\u006E\u0066\u006F\u0072\u006D\u0061\u0074\u0069\u006F\u006E\u0044\u0061\u0074\u0061']);break;case this['\u0043\u004D\u0044\u005F\u0053\u0045\u0054\u005F\u0052\u0046\u0049\u0044\u005F\u0044\u0041\u0054\u0041']:this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();break;case this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045']:this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();break;case this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041']:if(baseSupPrint['\u0068\u0061\u006E\u0064\u006C\u0065\u0049\u006E\u0071\u0075\u0069\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073'](notifyMessage)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0053\u0074\u0065\u0070']();break;case this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);break;case this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052']:this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0054\u0068\u0069\u0072\u0064']();break;case this['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C']:this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']=560594^560634;this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']=615885^615909;this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']=968583^968623;this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']++;console['\u006C\u006F\u0067']("\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u63A5\u6536\u5230\u5FD7\u6EE1\u5B8C\u6210\u547D\u4EE4\u56DE\u590D\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A\u002A");if(this['\u0063\u006F\u0070\u0069\u0065\u0073\u0041\u006C\u006C\u004E\u0075\u006D']>=bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u0049\u004D\u0041\u0047\u0045']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0049\u006D\u0061\u0067\u0065']();}else if(this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']==constants['\u0050\u0052\u0049\u004E\u0054\u005F\u0054\u0059\u0050\u0045\u005F\u004D\u0041\u0054\u0052\u0049\u0058']){bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004D\u0061\u0074\u0072\u0069\u0078']();}}break;case this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054']:this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=538458^538458;if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();break;}},handleStep(){switch(this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']){case"\u0069\u006E\u0069\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(197105^197104)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0063\u0068\u0065\u0063\u006B\u0044\u0065\u0076\u0069\u0063\u0065";this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0043\u0048\u0045\u0043\u004B\u005F\u0044\u0045\u0056\u0049\u0043\u0045'],987302^987302);break;case"\u0073\u0074\u0061\u0072\u0074\u0050\u0072\u0069\u006E\u0074":if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u7EC8\u6B62\u6253\u5370\u6210\u529F",constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031']));this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u529F\u6210\u5370\u6253\u6B62\u7EC8".split("").reverse().join(""),constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073']));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();}else{setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},194516^194332);}break;case"\u0073\u0065\u006E\u0064\u004D\u0061\u0074\u0072\u0069\u0078\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":if(ptFlag['\u0062\u0075\u0066\u0053\u0074\u0061']==(458199^458199)){if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(232241^232240)){this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070'](this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0074\u0053\u0074\u0061\u0072\u004E\u0075\u006D']<=(847464^847464)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},297542^297588);}}else{this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']--;if(this['\u0062\u0075\u0066\u0053\u0074\u0061\u004E\u0075\u006D']<=(232959^232959)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}setTimeout(()=>{this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},110515^110465);}break;case"\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073":this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u004F\u0050\u005F\u0050\u0052\u0049\u004E\u0054'],523951^523951);break;case"eciveDkcehc".split("").reverse().join(""):this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']--;if(this['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E\u004E\u0075\u006D']<=(509456^509456)){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](false,!![])){return;}if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u006D\u0045\u0078\u0065\u0053\u0074\u0061']==(669816^669816)){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="tnirPtrats".split("").reverse().join("");this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0053\u0054\u0041\u0054\u0052\u005F\u0050\u0052\u0049\u004E\u0054'],795882^795882);}else{setTimeout(()=>{this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},302550^302564);}break;case"\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068":console['\u006C\u006F\u0067']("noitatSgnitnirp".split("").reverse().join(""),ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']);if(ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(153734^153735)){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}else{this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']=ptFlag['\u0070\u0050\u0061\u0067\u0065\u0043\u006E\u0074'];if(this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']==(731082^731082)){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006E\u0075\u006D':this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u0070\u0072\u0069\u006E\u0074\u004E\u0075\u006D']}));}else{this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']));}}break;}},handleTransferNext(){if(this['\u0069\u0073\u0053\u0074\u006F\u0070']){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u7EC8\u6B62\u6253\u5370\u6210\u529F",constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031']));this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']("\u529F\u6210\u5370\u6253\u6B62\u7EC8".split("").reverse().join(""),constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073']));this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();return;}this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="sutatSyreuQxirtaMdnes".split("").reverse().join("");this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},handleTransferStep(btData){if(this['\u0064\u0065\u0076\u0043\u0068\u0065\u0063\u006B\u0045\u0072\u0072\u004D\u0073\u0067'](!![],!![])){this['\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074']();if(this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']){return;}return;}this['\u0063\u006E\u0074']=parseInt((btData['\u006C\u0065\u006E\u0067\u0074\u0068']+this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']-(650478^650479))/this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050\u0053\u0074\u0061\u0072\u0074\u0054\u0072\u0061\u006E\u0073'](this['\u0043\u004D\u0044\u005F\u004E\u0045\u0058\u0054\u005F\u005A\u0049\u0050\u0050\u0045\u0044\u0042\u0055\u004C\u004B'],395624^396136,this['\u0063\u006E\u0074']);},handleTransferStepThird(){var _0x7a51gb=(948821^948817)+(433496^433496);const that=this;_0x7a51gb=444371^444373;that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']++;if(that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']<that['\u0063\u006E\u0074']){that['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0053\u0065\u0063\u006F\u006E\u0064'](that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']);}else{setTimeout(()=>{that['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']=464828^464828;that['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050\u0053\u0074\u0061\u0072\u0074\u0054\u0072\u0061\u006E\u0073'](that['\u0043\u004D\u0044\u005F\u0042\u0055\u0046\u005F\u0046\u0055\u004C\u004C'],that['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']['\u006C\u0065\u006E\u0067\u0074\u0068'],that['\u0073\u0070\u0065\u0065\u0064']);},239292^239246);}},handleTransferStepSecond(btData){var _0x57eaf=(873529^873528)+(866423^866421);let btWrite=new Uint8Array(this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']+(634400^634406));_0x57eaf=(150144^150147)+(200549^200557);btWrite[423334^423334]=694022^694188;btWrite[856001^856000]=135431^135612;btWrite[370273^370275]=767747^767747;btWrite[508484^508487]=592461^592461;btWrite[340869^340865]=this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072'];btWrite[107153^107156]=this['\u0063\u006E\u0074'];if((this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+(972096^972097))*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']<btData['\u006C\u0065\u006E\u0067\u0074\u0068']){for(var k=719747^719747;k<this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];k++){btWrite[(816414^816408)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}else{let nc=btData['\u006C\u0065\u006E\u0067\u0074\u0068']-this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']*this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068'];for(var k=227162^227162;k<nc;k++){btWrite[(644104^644110)+k]=btData[this['\u0064\u0061\u0074\u0061\u004C\u0065\u006E\u0067\u0074\u0068']*this['\u0066\u0072\u0061\u006D\u0065\u004E\u0075\u006D\u0062\u0065\u0072']+k];}}let chcksum=117446^117446;for(var i=599642^599646;i<btWrite['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){chcksum+=btWrite[i]&(616597^616554);}btWrite[635028^635030]=chcksum;btWrite[723056^723059]=chcksum>>(571609^571601);this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0054\u0052\u0041\u004E\u0053\u0046\u0045\u0052'];this['\u0074\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0044\u0061\u0074\u0061\u004C\u0050'](btWrite);},transferDataLP(btdata){var _0xed54f=(560606^560603)+(934798^934793);let message=new Uint8Array(100602^101114);_0xed54f="aqhoif".split("").reverse().join("");message[817712^817712]=671470^671376;message[987773^987772]=226225^226283;message[111539^111537]=257630^257698;message[646927^646924]=373562^373563;message[488900^488896]=729297^729281;message[235885^235880]=219838^219836;for(var i=133369^133375;i<btdata['\u006C\u0065\u006E\u0067\u0074\u0068']+(565501^565499);i++){message[i]=btdata[i-(556598^556592)];}this['\u0073\u0065\u006E\u0064\u0044\u0061\u0074\u0061\u0054\u0035\u0030\u0050\u0072\u006F'](message);},sendDataT50Pro(message){var _0xgd55ga=(201397^201399)+(961557^961558);const that=this;_0xgd55ga=241788^241785;var _0x65gb=(362817^362816)+(679148^679149);let byteArrayList=[];_0x65gb=(891356^891349)+(655009^655011);let i=365897^365897;var _0x6b1fa=(158366^158365)+(188254^188253);let count=566231^566231;_0x6b1fa='\u006A\u0065\u006E\u006A\u006A\u0069';while(i<(815304^815308)){var _0x_0xede;let listData=message['\u0073\u0075\u0062\u0061\u0072\u0072\u0061\u0079'](i*(993733^993605),(114136^114008)+i*(888535^888407));_0x_0xede=826735^826734;byteArrayList['\u0070\u0075\u0073\u0068'](listData);i++;}var timer=setInterval(()=>{if(count==(298611^298615)){clearInterval(timer);setTimeout(()=>{that['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u0053\u0074\u0065\u0070\u0054\u0068\u0069\u0072\u0064']();},364908^364894);return;}bleManage['\u006F\u006E\u0057\u0072\u0069\u0074\u0065\u0042\u004C\u0045\u0043\u0068\u0061\u0072\u0061\u0063\u0074\u0065\u0072\u0069\u0073\u0074\u0069\u0063\u0056\u0061\u006C\u0075\u0065'](byteArrayList[count]);count++;},600327^600373);},stopPrint(){this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0073\u0074\u006F\u0070\u0050\u0072\u0069\u006E\u0074\u0051\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073";console['\u006C\u006F\u0067']("\u6001\u72B6\u8BE2\u67E5".split("").reverse().join(""));this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();return false;},devCheckErrMsg(isBusy,isPrintFinish){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=false;if(ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']!=(251413^251413)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0036'];console['\u006C\u006F\u0067']("nepOrevoC".split("").reverse().join(""),ptFlag['\u0046\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0043\u006F\u0076\u0065\u0072\u004F\u0070\u0065\u006E']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']!=(132814^132814)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0037'];console['\u006C\u006F\u0067']("\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064",ptFlag['\u006E\u006F\u0074\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0065\u0064']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0045\u006E\u0064']!=(422040^422040)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0038'];console['\u006C\u006F\u0067']("dnEbiR".split("").reverse().join(""),ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0045\u006E\u0064']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']!=(211261^211261)){this['\u006E\u006F\u0049\u006E\u0073\u0074\u0061\u006C\u006C\u0054\u0079\u0070\u0065']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0039'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072",ptFlag['\u006C\u0061\u0062\u0065\u006C\u0052\u0065\u0061\u0064\u0057\u0072\u0069\u0074\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']!=(425774^425774)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0030'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072",ptFlag['\u006C\u0061\u0062\u0065\u006C\u004D\u006F\u0064\u0065\u0045\u0072\u0072\u006F\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']!=(684583^684583)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0031'];console['\u006C\u006F\u0067']("\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065\u72B6\u0031\u6001\u503C",ptFlag['\u006C\u0061\u0062\u0065\u006C\u004E\u006F\u004D\u006F\u0072\u0065']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}else if(ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']!=(986026^986026)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0033'];console['\u006C\u006F\u0067']("\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072\u72B6\u0031\u6001\u503C",ptFlag['\u004D\u0053\u0054\u0041\u005F\u0052\u0045\u0047']['\u0052\u0069\u0062\u0052\u0077\u0045\u0072\u0072']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}if(isBusy){if(ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']==(397921^397921)&&ptFlag['\u0070\u0072\u0069\u006E\u0074\u0069\u006E\u0067\u0053\u0074\u0061\u0074\u0069\u006F\u006E']==(906238^906238)){this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0033\u0032'];console['\u006C\u006F\u0067']("ysuBecived".split("").reverse().join(""),ptFlag['\u0064\u0065\u0076\u0069\u0063\u0065\u0042\u0075\u0073\u0079']);if(isPrintFinish){this['\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068']();}return!![];}}return false;},doPrintNextPage(nImageDataListNext){let imageDataListNext=nImageDataListNext;this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=[];this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']=imageDataListNext;console['\u006C\u006F\u0067']("\u7B2C\u4E8C\u4EFD\u6253\u5370\u5B57\u6A21\u957F\u5EA6\u003A\u0020",this['\u0069\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061\u004C\u0069\u0073\u0074']['\u006C\u0065\u006E\u0067\u0074\u0068']);this['\u0068\u0061\u006E\u0064\u006C\u0065\u0054\u0072\u0061\u006E\u0073\u0066\u0065\u0072\u004E\u0065\u0078\u0074']();},printCallback(callback,type){this['\u0063\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callback;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;},printFinish(){setTimeout(()=>{this['\u0070\u0072\u006F\u0063\u0065\u0073\u0073\u0053\u0074\u0065\u0070\u0073']="\u0070\u0072\u0069\u006E\u0074\u0046\u0069\u006E\u0069\u0073\u0068";this['\u0071\u0075\u0065\u0072\u0079\u0053\u0074\u0061\u0074\u0075\u0073']();},298667^298687);},stopPrintSupvan(){this['\u0069\u0073\u0053\u0074\u006F\u0070']=!![];this['\u0070\u0072\u0069\u006E\u0074\u004D\u0065\u0073\u0073\u0061\u0067\u0065']['\u006D\u0073\u0067\u0054\u0079\u0070\u0065']=constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0053\u0075\u0063\u0063\u0065\u0073\u0073'];},queryStatus(){this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0049\u004E\u0051\u0055\u0049\u0052\u0059\u005F\u0053\u0054\u0041'],298873^298873);},stopPrintCallback(stopCallBack){this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=stopCallBack;},readConsumableMessage(callBack){this['\u006D\u0061\u0074\u0043\u0061\u006C\u006C\u0062\u0061\u0063\u006B']=callBack;this['\u0063\u0075\u0072\u0072\u0065\u006E\u0074\u0043\u006F\u006D\u006D\u0061\u006E\u0064']=this['\u0043\u004D\u0044\u005F\u0052\u0045\u0054\u0055\u0052\u004E\u005F\u004D\u0041\u0054'];baseSupPrint['\u0073\u0065\u006E\u0064\u0043\u006D\u0064\u004C\u0050'](this['\u0043\u004D\u0044\u005F\u0052\u0045\u0054\u0055\u0052\u004E\u005F\u004D\u0041\u0054'],872567^872567);}};