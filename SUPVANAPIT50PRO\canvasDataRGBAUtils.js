import imageEncodeUtilsT50Pro from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0054\u0035\u0030\u0050\u0072\u006F\u002E\u006A\u0073";import imageEncodeUtilsMP50 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u004D\u0050\u0035\u0030\u002E\u006A\u0073";import imageEncodeUtilsG15 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0031\u0035\u002E\u006A\u0073";import imageEncodeUtilsG21 from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0069\u006D\u0061\u0067\u0065\u0045\u006E\u0063\u006F\u0064\u0065\u0055\u0074\u0069\u006C\u0073\u0047\u0032\u0031\u002E\u006A\u0073";import bleToothManage from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u0074\u0068\u004D\u0061\u006E\u0061\u0067\u0065\u002E\u006A\u0073";import bleTool from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0042\u004C\u0045\u0054\u006F\u006F\u006C\u002E\u006A\u0073";import lpapi from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u004C\u0050\u0041\u0050\u0049\u002F\u004C\u0050\u0041\u0050\u0049\u002E\u006A\u0073";import constants from"\u002E\u002E\u002F\u0053\u0055\u0050\u0056\u0041\u004E\u0041\u0050\u0049\u0054\u0035\u0030\u0050\u0052\u004F\u002F\u0043\u006F\u006E\u0073\u0074\u0061\u006E\u0074\u0073\u002E\u006A\u0073";export default{"myCanvasRGBA":null,'\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F':1,'\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065':8,"objectValueLocal":{},'\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074':[],"imageObjectDT":[],'\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,'\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B':null,"dtPrintSuccessCallBack":null,'\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065':'','\u0063\u0061\u006E\u0063\u0065\u006C\u0044\u0074\u0050\u0072\u0069\u006E\u0074':false,'\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074':null,"qrCodePath":'',"imagePath":'',async matrixDataCanvas(nObjectData){try{var _0xfg8bec=(908695^908688)+(967742^967736);const that=this;_0xfg8bec=873804^873805;let objectData=nObjectData;that['\u0064\u0061\u0074\u0061\u0049\u006E\u0069\u0074'](objectData);for(let i in that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073']){console['\u006C\u006F\u0067']("lacoLeulaVtcejbo.taht".split("").reverse().join(""),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']);that['\u0064\u0072\u0061\u0077\u0053\u0077\u0069\u0074\u0063\u0068\u0046\u006F\u0072\u006D\u0061\u0074'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073'][i]);}await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();await that['\u006D\u0061\u0074\u0072\u0069\u0078\u0043\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();}catch(error){throw error;}},async previewDataCanvas(nObjectData){try{const that=this;let objectData=nObjectData;that['\u0064\u0061\u0074\u0061\u0049\u006E\u0069\u0074'](objectData);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0072\u0065\u0076\u0069\u0065\u0077\u0050\u0061\u0074\u0068']){await that['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0072\u0065\u0076\u0069\u0065\u0077\u0050\u0061\u0074\u0068']);}for(let i in objectData['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073']){that['\u0064\u0072\u0061\u0077\u0053\u0077\u0069\u0074\u0063\u0068\u0046\u006F\u0072\u006D\u0061\u0074'](objectData['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C']['\u0044\u0072\u0061\u0077\u004F\u0062\u006A\u0065\u0063\u0074\u0073'][i]);}await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();}catch(error){throw error;}},async dataInit(nObjectData){var _0xd70d4b=(584705^584709)+(544180^544176);const that=this;_0xd70d4b=(259505^259506)+(530524^530522);var _0xd44e8b;let objectData=nObjectData;_0xd44e8b=(379074^379076)+(923454^923453);let res=wx['\u0067\u0065\u0074\u0057\u0069\u006E\u0064\u006F\u0077\u0049\u006E\u0066\u006F']();that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F']=res['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'];that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']=objectData['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u0044\u0061\u0074\u0061'];that['\u0071\u0072\u0043\u006F\u0064\u0065\u0050\u0061\u0074\u0068']=objectData['\u0071\u0072\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'];that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']=objectData['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'];that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']=objectData['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'];that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']=objectData['\u006F\u0062\u006A\u0065\u0063\u0074\u0041\u006C\u006C'];that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']=bleTool['\u0067\u0065\u0074\u0046\u0044\u0070\u0069\u0056\u0061\u006C\u0075\u0065']();that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0063\u006C\u0065\u0061\u0072\u0052\u0065\u0063\u0074'](578469^578469,954798^954798,that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F']);},async drawImagePath(path){try{var _0xedd82d=(912939^912937)+(245827^245830);const that=this;_0xedd82d='\u006D\u0066\u0064\u0069\u0069\u0068';const loadFileRes=await that['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'](path);if(loadFileRes){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](loadFileRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(129006^129004),(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']-that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'])/(563330^563328),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);}}catch(error){throw error;}},async imageDataCanvas(nObjectData){try{const that=this;let objectData=nObjectData;that['\u0064\u0061\u0074\u0061\u0049\u006E\u0069\u0074'](objectData);await that['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0049\u006D\u0061\u0067\u0065\u0055\u0072\u006C']);await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();await that['\u0069\u006D\u0061\u0067\u0065\u0043\u0061\u006E\u0076\u0061\u0073\u0044\u0072\u0061\u0077']();}catch(error){throw error;}},drawSwitchFormat(nMatrixObject){const that=this;let matrixObject=nMatrixObject;switch(matrixObject['\u0046\u006F\u0072\u006D\u0061\u0074']){case"\u0042\u0041\u0052\u0043\u004F\u0044\u0045":var _0xa02d=(672880^672885)+(467290^467294);let barCodeX=matrixObject['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0xa02d=827872^827873;let barCodeY=matrixObject['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];if(that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0070\u0061\u0074\u0068']){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0069\u006D\u0061\u0067\u0065\u0053\u006D\u006F\u006F\u0074\u0068\u0069\u006E\u0067\u0045\u006E\u0061\u0062\u006C\u0065\u0064']=false;that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0070\u0061\u0074\u0068'],barCodeX,barCodeY,that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0077\u0069\u0064\u0074\u0068'],that['\u0062\u0061\u0072\u0043\u006F\u0064\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0068\u0065\u0069\u0067\u0068\u0074']);}break;case"\u0051\u0052\u0043\u004F\u0044\u0045":if(that['\u0071\u0072\u0043\u006F\u0064\u0065\u0050\u0061\u0074\u0068']){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0069\u006D\u0061\u0067\u0065\u0053\u006D\u006F\u006F\u0074\u0068\u0069\u006E\u0067\u0045\u006E\u0061\u0062\u006C\u0065\u0064']=false;var _0x0966f=(452686^452684)+(759559^759552);let qrCodeX=matrixObject['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0x0966f="npgnjm".split("").reverse().join("");var _0xab5cfd=(870992^870997)+(340858^340856);let qrCodeY=matrixObject['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0xab5cfd=(226322^226323)+(734871^734869);let qrWidth=matrixObject['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];let qrHeight=matrixObject['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];console['\u006C\u006F\u0067']("tcejbOxirtam".split("").reverse().join(""),matrixObject);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](that['\u0071\u0072\u0043\u006F\u0064\u0065\u0050\u0061\u0074\u0068'],qrCodeX,qrCodeY,qrWidth,qrHeight);}break;case"EGAMI".split("").reverse().join(""):if(that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068']){that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065'](that['\u0069\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'],matrixObject['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],matrixObject['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],matrixObject['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],matrixObject['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);}break;case"TXET".split("").reverse().join(""):that['\u0064\u0072\u0061\u0077\u0054\u0065\u0078\u0074'](that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041'],matrixObject);break;}},async downloadFile(url){try{return await constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0064\u006F\u0077\u006E\u006C\u006F\u0061\u0064\u0046\u0069\u006C\u0065'],{'\u0075\u0072\u006C':url});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0033'],error);}},canvasDraw(){return new Promise((resolve,reject)=>{var _0x7a9ef;const that=this;_0x7a9ef=887537^887542;that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0073\u0063\u0061\u006C\u0065'](that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F']);that['\u006D\u0079\u0043\u0061\u006E\u0076\u0061\u0073\u0052\u0047\u0042\u0041']['\u0064\u0072\u0061\u0077'](false,()=>{setTimeout(()=>{resolve();},480283^480567);});});},async matrixCanvasDraw(){try{const that=this;if(bleTool['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){var _0x5c18ac;const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']();_0x5c18ac=(227724^227727)+(255660^255652);for(let index=111004^111004;index<that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0043\u006F\u0070\u0069\u0065\u0073'];index++){that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u0070\u0075\u0073\u0068'](filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']);}if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u006C\u0065\u006E\u0067\u0074\u0068']<bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){await bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004D\u0061\u0074\u0072\u0069\u0078']();}else{await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u0044\u0054']();}}else{var _0xba5d;const imageRgbaRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0047\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();_0xba5d=(889098^889098)+(819603^819607);await that['\u006D\u006F\u0064\u0065\u006C\u0049\u006E\u0064\u0065\u006E\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006F\u006E'](imageRgbaRes);}}catch(error){throw error;}},async modelIndentification(imageRgbaRes){try{var _0x631adb=(496717^496708)+(959690^959693);const that=this;_0x631adb=(453503^453502)+(860185^860187);if(bleTool['\u0074\u0035\u0030\u0050\u0072\u006F\u0041\u006E\u0064\u0054\u0038\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsT50Pro['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}else if(bleTool['\u006D\u0070\u0035\u0030\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsMP50['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}else if(bleTool['\u0067\u0053\u0065\u0072\u0069\u0065\u0073\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsG15['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}else if(bleTool['\u0067\u0032\u0031\u004F\u0072\u0047\u0032\u0038\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){await imageEncodeUtilsG21['\u0069\u006E\u0069\u0074\u0045\u006E\u0063\u006F\u0064\u0065\u0044\u0061\u0074\u0061'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C'],imageRgbaRes['\u0064\u0061\u0074\u0061']);}}catch(error){throw error;}},async previewCanvasDraw(){try{const that=this;var _0x3d97b=(748824^748828)+(228717^228715);const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']();_0x3d97b=(569149^569151)+(549411^549419);that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u0070\u0075\u0073\u0068'](filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']);if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']['\u006C\u0065\u006E\u0067\u0074\u0068']<bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){bleToothManage['\u0064\u0072\u0061\u0077\u004E\u0065\u0078\u0074\u0050\u0072\u0065\u0076\u0069\u0065\u0077']();}else{that['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({"previewList":that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']}));}}catch(error){throw error;}},async imageCanvasDraw(){try{const that=this;if(bleTool['\u0064\u0074\u0042\u006C\u0065\u0044\u0065\u0076\u0069\u0063\u0065\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u0076\u0069\u0063\u0065\u0053\u006E'])){const filePathRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']();for(let index=940999^940999;index<that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0043\u006F\u0070\u0069\u0065\u0073'];index++){that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u0070\u0075\u0073\u0068'](filePathRes['\u0074\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']);}if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u006C\u0065\u006E\u0067\u0074\u0068']<bleToothManage['\u0067\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0041\u006C\u006C\u004E\u0075\u006D']()){await bleToothManage['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u0049\u006D\u0061\u0067\u0065']();}else{await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u0044\u0054']();}}else{const imageRgbaRes=await that['\u0063\u0061\u006E\u0076\u0061\u0073\u0047\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061']();await that['\u006D\u006F\u0064\u0065\u006C\u0049\u006E\u0064\u0065\u006E\u0074\u0069\u0066\u0069\u0063\u0061\u0074\u0069\u006F\u006E'](imageRgbaRes);}}catch(error){throw error;}},drawText(ctx,textData){const that=this;let mFontSize=365233^365233;let mAutoReturnType=false;var _0x858d6g=(140609^140613)+(817538^817541);let zoom=680160^680161;_0x858d6g=131126^131135;ctx['\u0073\u0061\u0076\u0065']();ctx['\u0069\u006D\u0061\u0067\u0065\u0053\u006D\u006F\u006F\u0074\u0068\u0069\u006E\u0067\u0045\u006E\u0061\u0062\u006C\u0065\u0064']=false;var _0xdc83a=(282054^282051)+(618599^618606);let fontSize=Math['\u0066\u006C\u006F\u006F\u0072'](textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);_0xdc83a=(635610^635612)+(122582^122582);var _0x_0x52g=(850549^850546)+(464420^464423);let fontWeight="004".split("").reverse().join("");_0x_0x52g=214967^214975;if(textData['\u0046\u006F\u006E\u0074\u0053\u0074\u0079\u006C\u0065']==(679965^679967)){fontWeight="dlob".split("").reverse().join("");}ctx['\u0066\u006F\u006E\u0074']=`normal ${fontWeight} ${fontSize}px sans-serif`;if(textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']==(272973^272973)){mFontSize=252123^252120;}else{ctx['\u0073\u0065\u0074\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065'](textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);mFontSize=textData['\u0046\u006F\u006E\u0074\u0053\u0069\u007A\u0065'];}var _0xbe_0x976=(479430^479428)+(672609^672613);let textY=textData['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(textData['\u0048\u0065\u0069\u0067\u0068\u0074']-mFontSize)/(198347^198345)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+mFontSize*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];_0xbe_0x976=(859335^859333)+(958853^958861);const metrics=ctx['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);const zoomScal=metrics['\u0077\u0069\u0064\u0074\u0068']/(textData['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u0074\u0065\u0078\u0074\u0044\u0061\u0074\u0061\u002E\u0043\u006F\u006E\u0074\u0065\u006E\u0074",textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);console['\u006C\u006F\u0067']("htdiw.scirtem".split("").reverse().join(""),metrics);console['\u006C\u006F\u0067']("\u0074\u0065\u0078\u0074\u0044\u0061\u0074\u0061\u002E\u0057\u0069\u0064\u0074\u0068",textData['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);console['\u006C\u006F\u0067']("\u007A\u006F\u006F\u006D\u0053\u0063\u0061\u006C",zoomScal);console['\u006C\u006F\u0067']("\u0061\u0075\u0074\u006F\u0052\u0065\u0074\u0075\u0072\u006E",textData['\u0041\u0075\u0074\u006F\u0052\u0065\u0074\u0075\u0072\u006E']);if(textData['\u0041\u0075\u0074\u006F\u0052\u0065\u0074\u0075\u0072\u006E']){zoom=449259^449258;if(zoomScal>1.1){textY=textData['\u0059']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+(textData['\u0048\u0065\u0069\u0067\u0068\u0074']-mFontSize*(119601^119603))/(652495^652493)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']+mFontSize*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'];mAutoReturnType=!![];}}else{if(zoomScal>(271777^271776)){zoom=textData['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']/metrics['\u0077\u0069\u0064\u0074\u0068'];}else{zoom=482506^482507;}}ctx['\u0074\u0065\u0078\u0074\u0042\u0061\u0073\u0065\u006C\u0069\u006E\u0065']="cihpargoedi".split("").reverse().join("");console['\u006C\u006F\u0067']("epyTrepap".split("").reverse().join(""),that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']);console['\u006C\u006F\u0067']("\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C\u002E\u0057\u0069\u0064\u0074\u0068",that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']);console['\u006C\u006F\u0067']("\u0074\u0065\u0078\u0074\u0044\u0061\u0074\u0061\u002E\u0057\u0069\u0064\u0074\u0068",textData['\u0057\u0069\u0064\u0074\u0068']);console['\u006C\u006F\u0067']("mooz".split("").reverse().join(""),zoom);ctx['\u0073\u0063\u0061\u006C\u0065'](zoom,713873^713872);var _0x8e18c=(243691^243689)+(862992^862997);let textX=752851^752851;_0x8e18c="kpdecp".split("").reverse().join("");if(textData['\u004F\u0072\u0069\u0065\u006E\u0074\u0061\u0074\u0069\u006F\u006E']==(909979^909978)){ctx['\u0073\u0065\u0074\u0054\u0065\u0078\u0074\u0041\u006C\u0069\u0067\u006E']("\u0063\u0065\u006E\u0074\u0065\u0072");textX=(textData['\u0058']+textData['\u0057\u0069\u0064\u0074\u0068']/(132304^132306))*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']/zoom;}else{ctx['\u0073\u0065\u0074\u0054\u0065\u0078\u0074\u0041\u006C\u0069\u0067\u006E']("tfel".split("").reverse().join(""));textX=textData['\u0058']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']/zoom;}if(!textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']){return;}if(mAutoReturnType){let objectData={'\u0063\u0074\u0078':ctx,'\u0063\u006F\u006E\u0074\u0065\u006E\u0074':textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'],"drawX":textX,'\u0064\u0072\u0061\u0077\u0059':textY,'\u006C\u0069\u006E\u0065\u0048\u0065\u0069\u0067\u0068\u0074':(textData['\u0048\u0065\u0069\u0067\u0068\u0074']+(644051^644050))/(611179^611177)*this['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u006C\u0069\u006E\u0065\u004D\u0061\u0078\u0057\u0069\u0064\u0074\u0068':(textData['\u0057\u0069\u0064\u0074\u0068']-0.5)*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"lineNum":2,"isTitle":false};this['\u0075\u0073\u0065\u0054\u0065\u0078\u0074\u0050\u0072\u0065\u0077\u0072\u0061\u0070'](objectData);}else{console['\u006C\u006F\u0067']("\u0043\u006F\u006E\u0074\u0065\u006E\u0074",textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074']);ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](textData['\u0043\u006F\u006E\u0074\u0065\u006E\u0074'],textX,textY);}ctx['\u0072\u0065\u0073\u0074\u006F\u0072\u0065']();},useTextPrewrap(objectData){let ctx=objectData['\u0063\u0074\u0078'];var _0xd12eg=(507341^507339)+(169728^169736);let content=objectData['\u0063\u006F\u006E\u0074\u0065\u006E\u0074'];_0xd12eg="glofql".split("").reverse().join("");let drawX=objectData['\u0064\u0072\u0061\u0077\u0058'];var _0xb1e;let drawY=objectData['\u0064\u0072\u0061\u0077\u0059'];_0xb1e=(236364^236357)+(481301^481309);let lineHeight=objectData['\u006C\u0069\u006E\u0065\u0048\u0065\u0069\u0067\u0068\u0074'];var _0xa97fc;let lineMaxWidth=objectData['\u006C\u0069\u006E\u0065\u004D\u0061\u0078\u0057\u0069\u0064\u0074\u0068'];_0xa97fc=(152541^152543)+(630483^630491);let lineNum=objectData['\u006C\u0069\u006E\u0065\u004E\u0075\u006D'];var _0x013f;let isTitle=objectData['\u0069\u0073\u0054\u0069\u0074\u006C\u0065'];_0x013f=(883779^883786)+(712341^712340);var drawTxt='';var drawLine=190507^190506;var _0xegea1f=(209748^209751)+(651145^651137);var drawIndex=777027^777027;_0xegea1f='\u006E\u0069\u0068\u006C\u0062\u006E';var _0x5fc3a;let reg=new RegExp("]5effu\\|6202u\\|5103u\\|d003u\\|f003u\\|1103u\\|9003u\\|b003u\\|90ffu\\|9102u\\|d102u\\|a1ffu\\|b1ffu\\|1003u\\|c0ffu\\|10ffu\\|f1ffu\\|2003u\\[".split("").reverse().join(""),"");_0x5fc3a=(286365^286364)+(335811^335814);if(ctx['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](content)['\u0077\u0069\u0064\u0074\u0068']<=lineMaxWidth){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i),drawX,drawY);}else{for(var i=195676^195676;i<content['\u006C\u0065\u006E\u0067\u0074\u0068'];i++){drawTxt+=content[i];if(content[i]==="\u000A"){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i+(301973^301972)),drawX,drawY);drawIndex=i+(755626^755627);drawLine+=312085^312084;drawY+=lineHeight;drawTxt='';if(drawLine>=lineNum){break;}else{continue;}};if(ctx['\u006D\u0065\u0061\u0073\u0075\u0072\u0065\u0054\u0065\u0078\u0074'](drawTxt)['\u0077\u0069\u0064\u0074\u0068']>=lineMaxWidth&&!reg['\u0074\u0065\u0073\u0074'](content[i+(174834^174835)])&&i!==content['\u006C\u0065\u006E\u0067\u0074\u0068']-(798350^798351)){if(drawLine>=lineNum){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i+(376709^376708))+"..".split("").reverse().join(""),drawX,drawY);break;}else{ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex,i+(461606^461607)),drawX,drawY);drawIndex=i+(675095^675094);if(content[i+(360685^360684)]!=="\u000A"){drawLine+=301840^301841;drawY+=lineHeight;}drawTxt='';}}else{if(i===content['\u006C\u0065\u006E\u0067\u0074\u0068']-(350773^350772)){ctx['\u0066\u0069\u006C\u006C\u0054\u0065\u0078\u0074'](content['\u0073\u0075\u0062\u0073\u0074\u0072\u0069\u006E\u0067'](drawIndex),drawX,drawY);}}}}},async canvasGetImageData(){try{const that=this;console['\u006C\u006F\u0067']("\u0057\u0069\u0064\u0074\u0068",that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']);console['\u006C\u006F\u0067']("\u0048\u0065\u0069\u0067\u0068\u0074",that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']);console['\u006C\u006F\u0067']("\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065",that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']);let objectData={'\u0063\u0061\u006E\u0076\u0061\u0073\u0049\u0064':"\u0043\u0061\u006E\u0076\u0061\u0073",'\u0078':0,'\u0079':0,'\u0077\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0068\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']};return await constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0047\u0065\u0074\u0049\u006D\u0061\u0067\u0065\u0044\u0061\u0074\u0061'],objectData);}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0032'],error);}},async canvasToTempFilePath(){try{var _0xgfc21e=(358165^358164)+(831750^831750);const that=this;_0xgfc21e=(670898^670896)+(733030^733026);let objectData={'\u0078':0,'\u0079':0,'\u0077\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],"height":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0064\u0065\u0073\u0074\u0057\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],"destHeight":that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],"canvasId":'Canvas','\u0066\u0069\u006C\u0065\u0054\u0079\u0070\u0065':'png','\u0071\u0075\u0061\u006C\u0069\u0074\u0079':1.0};return await constants['\u0077\u0078\u0050\u0072\u006F\u006D\u0069\u0073\u0065'](wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068'],objectData);}catch(error){console['\u006C\u006F\u0067']("\u0031\u0065\u0072\u0072\u006F\u0072",error);return await this['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068\u0054\u0077\u006F']();}},async canvasToTempFilePathTwo(){const that=this;return new Promise((resolve,reject)=>{wx['\u0063\u0061\u006E\u0076\u0061\u0073\u0054\u006F\u0054\u0065\u006D\u0070\u0046\u0069\u006C\u0065\u0050\u0061\u0074\u0068']({'\u0078':0,'\u0079':0,'\u0077\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0068\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065'],'\u0064\u0065\u0073\u0074\u0057\u0069\u0064\u0074\u0068':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],'\u0064\u0065\u0073\u0074\u0048\u0065\u0069\u0067\u0068\u0074':that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074']*that['\u0064\u0070\u0069\u0056\u0061\u006C\u0075\u0065']*that['\u0070\u0069\u0078\u0065\u006C\u0052\u0061\u0074\u0069\u006F'],"canvasId":"\u0043\u0061\u006E\u0076\u0061\u0073","fileType":"\u0070\u006E\u0067","quality":1.0,'\u0073\u0075\u0063\u0063\u0065\u0073\u0073':res=>{console['\u006C\u006F\u0067']("\u529F\u6210\u7247\u56FE\u7801\u5F62\u6761\u6210\u751F".split("").reverse().join(""),res);resolve(res);},'\u0066\u0061\u0069\u006C':error=>{console['\u006C\u006F\u0067']("\u751F\u6210\u6761\u5F62\u7801\u56FE\u7247\u5931\u8D25",error);reject(constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0034'],error));}},this);});},async doPrintDT(){try{const that=this;lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0070\u0065\u0065\u0064'](690944^690944);if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(129575^129574)){lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0050\u0061\u0067\u0065\u0047\u0061\u0070\u0054\u0079\u0070\u0065'](648716^648718);}else if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0050\u0061\u0070\u0065\u0072\u0054\u0079\u0070\u0065']==(786132^786129)){lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0050\u0061\u0067\u0065\u0047\u0061\u0070\u0054\u0079\u0070\u0065'](967886^967885);}else{lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0050\u0061\u0067\u0065\u0047\u0061\u0070\u0054\u0079\u0070\u0065'](794880^794882);}lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0044\u0061\u0072\u006B\u006E\u0065\u0073\u0073'](that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0044\u0065\u006E\u0073\u0069\u0074\u0079']);lpapi['\u0073\u0065\u0074\u0050\u0072\u0069\u006E\u0074\u0047\u0072\u0061\u0079\u0054\u0068\u0072\u0065\u0073\u0068\u006F\u006C\u0064'](590554^590362);var _0xd6957b;let widthDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0057\u0069\u0064\u0074\u0068'];_0xd6957b=(480881^480882)+(685174^685170);let heightDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u0065\u0069\u0067\u0068\u0074'];let horizontalNumDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0048\u006F\u0072\u0069\u007A\u006F\u006E\u0074\u0061\u006C\u004E\u0075\u006D'];var _0xdb_0x05d=(864409^864408)+(237597^237592);let verticalNumDt=that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0056\u0065\u0072\u0074\u0069\u0063\u0061\u006C\u004E\u0075\u006D'];_0xdb_0x05d=(472756^472756)+(972113^972121);let rotate=635171^635171;if(that['\u006F\u0062\u006A\u0065\u0063\u0074\u0056\u0061\u006C\u0075\u0065\u004C\u006F\u0063\u0061\u006C']['\u0052\u006F\u0074\u0061\u0074\u0065']==(353581^353580)){heightDt=heightDt-(603280^603282);verticalNumDt=verticalNumDt+(325196^325197);rotate=693483^693483;}else{widthDt=widthDt-(346479^346477);horizontalNumDt=horizontalNumDt+(328726^328727);rotate=439625^439571;}lpapi['\u0073\u0074\u0061\u0072\u0074\u0044\u0072\u0061\u0077\u004C\u0061\u0062\u0065\u006C'](widthDt,heightDt,rotate);var _0x1a1gg;let dtImg=this['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u0073\u0068\u0069\u0066\u0074']();_0x1a1gg=(502832^502833)+(823343^823335);lpapi['\u0064\u0072\u0061\u0077\u0049\u006D\u0061\u0067\u0065\u0050\u0061\u0074\u0068'](dtImg,horizontalNumDt,verticalNumDt,widthDt,heightDt,function(){that['\u0070\u0072\u0069\u006E\u0074\u004F\u006E\u0065\u004C\u0061\u0062\u0065\u006C']();});}catch(error){throw error;}},async printOneLabel(){try{var _0xdead;const that=this;_0xdead=(381488^381490)+(137383^137382);lpapi['\u0070\u0072\u0069\u006E\u0074'](function(){that['\u0070\u0072\u0069\u006E\u0074\u004E\u0065\u0078\u0074\u004C\u0061\u0062\u0065\u006C']();});}catch(error){throw constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0046\u0061\u0069\u006C'](constants['\u0067\u006C\u006F\u0062\u0061\u006C\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065']['\u0052\u0065\u0073\u0075\u006C\u0074\u0043\u006F\u0064\u0065\u0031\u0032\u0035'],error);}},async printNextLabel(){try{const that=this;if(that['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']['\u006C\u0065\u006E\u0067\u0074\u0068']==(699222^699222)){this['\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':"\u5168\u90E8\u6253\u5370\u5B8C\u6210"}));}else{if(!that['\u0063\u0061\u006E\u0063\u0065\u006C\u0044\u0074\u0050\u0072\u0069\u006E\u0074']){await that['\u0064\u006F\u0050\u0072\u0069\u006E\u0074\u0044\u0054']();}else{this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B'](constants['\u0072\u0065\u0073\u0075\u006C\u0074\u004F\u0062\u006A\u0065\u0063\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073']({'\u006D\u0073\u0067':'终止打印成功'}));}}}catch(error){throw error;}},stopPrintDt(dtPrint){var _0x56a4c=(764069^764076)+(998090^998088);const that=this;_0x56a4c=995232^995234;that['\u0063\u0061\u006E\u0063\u0065\u006C\u0044\u0074\u0050\u0072\u0069\u006E\u0074']=dtPrint;},cleanImageObject(){this['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074']=[];this['\u0069\u006D\u0061\u0067\u0065\u004F\u0062\u006A\u0065\u0063\u0074\u0044\u0054']=[];},drawPreviewCallback(previewCallBack){this['\u0070\u0072\u0065\u0076\u0069\u0065\u0077\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=previewCallBack;},stopPrintCallback(stopCallBack){this['\u0073\u0074\u006F\u0070\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=stopCallBack;},dtPrintSuccessCallback(dtPrintSuccessCallBack,type){this['\u0064\u0074\u0050\u0072\u0069\u006E\u0074\u0053\u0075\u0063\u0063\u0065\u0073\u0073\u0043\u0061\u006C\u006C\u0042\u0061\u0063\u006B']=dtPrintSuccessCallBack;this['\u0070\u0072\u0069\u006E\u0074\u0054\u0079\u0070\u0065']=type;}};